
import { CommonModule } from '@angular/common';
import { Component, input, OnInit } from '@angular/core';
import { IconComponent } from '../../../core/components/icon.component';
import { ButtonModule } from 'primeng/button';
import { CostItemModel } from '../models/cost-item.model';
import { RouterModule } from '@angular/router';

@Component({
    selector: 'bh-costs',
    templateUrl: 'costs.component.html',
    imports: [CommonModule, IconComponent, ButtonModule, RouterModule],
    standalone: true,
})

export class CostsComponent implements OnInit {

    title = input<string>();
    costItems = input<CostItemModel[]>([]);
    tooltip = input<string>();
    tooltipLink = input<string>();

    emptyPlaceholderText = input<string>();
    emptyButtonLink = input<string>();
    emptyButtonLabel = input<string>();

    constructor() { }

    ngOnInit() { }

    isToday(date: Date) {
        return date.toDateString() === new Date().toDateString();
    }
}