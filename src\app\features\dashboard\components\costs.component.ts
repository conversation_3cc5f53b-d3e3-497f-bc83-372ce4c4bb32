
import { CommonModule } from '@angular/common';
import { Component, inject, input, model, OnInit } from '@angular/core';
import { IconComponent } from '../../../core/components/icon.component';
import { ButtonModule } from 'primeng/button';
import { CostItemModel, CostResultModel } from '../models/cost-item.model';
import { RouterModule } from '@angular/router';
import { BaseDashboardComponent } from './base-dashboard.component';
import { DashboardDataService } from '../services/dashboard-data.service';
import { map } from 'rxjs';

@Component({
    selector: 'bh-costs',
    templateUrl: 'costs.component.html',
    imports: [CommonModule, IconComponent, ButtonModule, RouterModule],
    standalone: true,
    providers: [DashboardDataService],
})

export class CostsComponent extends BaseDashboardComponent implements OnInit {

    dataSource = input<'costs' | 'invoices' | 'cheques' | 'bonds' | 'credits'>('costs');

    dashboardDataService: DashboardDataService = inject(DashboardDataService);

    override loadData(): void {
        if (this.costItems().length > 0) return;

        this.loading.set(true);

        switch (this.dataSource()) {
            case 'costs':
                this.dashboardDataService.getCosts()
                    .pipe(map((data: CostResultModel) => {
                        data.items.forEach(x => {
                            x.avatar = this.getAvatar(x.title);
                            x.link = `web/ngn/acc/ngncostentry?rc=1&guid=${x.link}`;
                        })
                        return data;
                    })).subscribe(data => {
                        this.overduedCount.set(data.overdueCount ?? 0);
                        this.tooltip.set('Vadesi geçen <b>' + data.overdueCount + ' adet</b> masrafınız var.');
                        this.tooltipLink.set('/costs');

                        this.costItems.set(data.items);
                        this.loading.set(false);
                    });
                break;

            case 'invoices':
                this.dashboardDataService.getInvoices()
                    .pipe(map((data: CostResultModel) => {
                        data.items.forEach(x => {
                            x.avatar = this.getAvatar(x.title);
                            x.link = `web/ngn/pos/ngncustomer?rc=1&guid=${x.link}`;
                        })
                        return data;
                    })).subscribe(data => {
                        this.costItems.set(data.items);
                        this.loading.set(false);
                    });
                break;

            case 'cheques':
                this.dashboardDataService.getCheques()
                    .pipe(map((data: CostResultModel) => {
                        data.items.forEach(x => {
                            x.avatar = this.getAvatar(x.title);
                            x.link = `web/ngn/pos/ngncustomer?rc=1&guid=${x.link}`;
                        })
                        return data;
                    }))
                    .subscribe(data => {
                        this.overduedCount.set(data.overdueCount ?? 0);
                        this.tooltip.set('Vadesi geçen <b>' + data.overdueCount + ' adet</b> çekiniz var.');
                        this.tooltipLink.set('/cheques');

                        this.costItems.set(data.items);
                        this.loading.set(false);
                    });
                break;

            case 'bonds':
                this.dashboardDataService.getBonds()
                    .pipe(map((data: CostResultModel) => {
                        data.items.forEach(x => {
                            x.avatar = this.getAvatar(x.title);
                            x.link = `web/ngn/pos/ngncustomer?rc=1&guid=${x.link}`;
                        })
                        return data;
                    }))
                    .subscribe(data => {
                        this.overduedCount.set(data.overdueCount ?? 0);
                        this.tooltip.set('Vadesi geçen <b>' + data.overdueCount + ' adet</b> senetiniz var.');
                        this.tooltipLink.set('/bonds');

                        this.costItems.set(data.items);
                        this.loading.set(false);
                    });
                break;

            case 'credits':
                this.dashboardDataService.getCredits()
                    .pipe(map((data: CostResultModel) => {
                        data.items.forEach(x => {
                            x.avatar = this.getAvatar(x.title);
                            x.link = `web/ngn/acc/ngncredit?rc=1&guid=${x.link}`;
                        })
                        return data;
                    }))
                    .subscribe(data => {
                        this.overduedCount.set(data.overdueCount ?? 0);
                        this.tooltip.set('Vadesi geçen <b>' + data.overdueCount + ' adet</b> krediniz var.');
                        this.tooltipLink.set('/credits');

                        this.costItems.set(data.items);
                        this.loading.set(false);
                    });
                break;

            default:
                this.loading.set(false);
                break;
        }
    }

    getAvatar(value?: string) {
        if (!value) return '';
        const splittedValue = value.split(' ');
        if (splittedValue.length > 1) return (splittedValue[0].substring(0, 1) + splittedValue[splittedValue.length - 1].substring(0, 1)).toUpperCase();
        else return value.substring(0, 2).toUpperCase();
    }


    costItems = model<CostItemModel[]>([]);
    tooltip = model<string>();
    tooltipLink = model<string>();

    title = input<string>();
    emptyPlaceholderText = input<string>();
    emptyButtonLink = input<string>();
    emptyButtonLabel = input<string>();

    overduedCount = model<number>(0);

    ngOnInit() {
        this.loadData();
    }

    isToday(date: Date) {
        return date.toDateString() === new Date().toDateString();
    }
}