import { Component, input, output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../../core/components/icon.component';
import { DashboardError } from './base-dashboard.component';
import { ButtonComponent } from '../../../core/components/button/button.component';

@Component({
    selector: 'bh-dashboard-error',
    template: `
        <div class="flex flex-col items-center justify-center gap-4 p-6 bg-red-50 rounded-lg border border-red-200 min-h-32">
            <!-- Error Icon -->
            <div class="flex items-center justify-center w-12 h-12 bg-red-100 rounded-full">
                <bh-icon name="close" [size]="36" class="text-red-600"></bh-icon>
            </div>
            
            <!-- Error Message -->
            <div class="text-center">

                <p class="text-xs text-red-700">
                    {{ error().message || 'Beklenmeyen bir hata oluştu. Lütfen tekrar deneyin.' }}
                </p>
                @if(error().code) {
                    <p class="text-xs text-red-600 mt-1 font-mono">
                        Hata Kodu: {{ error().code }}
                    </p>
                }
            </div>
            
            <!-- Action Buttons -->
            <div class="flex gap-2">
                @if(showRetry() && (error().retryable ?? true)) {
                    <bh-button variant="outlined" size="sm"
                        (click)="onRetry.emit()">
                        Tekrar Dene
                    </bh-button>
                }
                
                @if(showSupport()) {
                    <bh-button variant="outlined" size="sm"
                        (click)="onSupport.emit()">
                        Destek
                    </bh-button>
                }
            </div>
        </div>
    `,
    imports: [CommonModule, IconComponent, ButtonComponent],
    standalone: true
})
export class DashboardErrorComponent {
    error = input.required<DashboardError>();
    showRetry = input<boolean>(true);
    showSupport = input<boolean>(false);

    onRetry = output<void>();
    onSupport = output<void>();
}
