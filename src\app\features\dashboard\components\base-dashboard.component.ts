import { Directive, model } from '@angular/core';

export interface DashboardError {
    message: string;
    code?: string;
    retryable?: boolean;
}

@Directive({
    host: {
        '[class.animate-pulse]': 'loading()'
    }
})
export abstract class BaseDashboardComponent {

    loading = model<boolean>(false);
    showValues = model<boolean>(true);
    error = model<DashboardError | null>(null);

    get hasError(): boolean {
        return this.error() !== null;
    }

    get isRetryable(): boolean {
        return this.error()?.retryable || false;
    }

    clearError(): void {
        this.error.set(null);
    }

    setError(error: DashboardError): void {
        this.error.set(error);
        this.loading.set(false);
    }

    retry(): void {
        this.clearError();
        this.loadData();
    }

    abstract loadData(): void;
}