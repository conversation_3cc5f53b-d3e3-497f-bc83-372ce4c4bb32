import { Injectable } from "@angular/core";
import { InMemoryDbService, RequestInfo, ResponseOptions, STATUS } from 'angular-in-memory-web-api';
import { delay, Observable, of } from "rxjs";

@Injectable({
    providedIn: 'root'
})
export class FakeBackendService implements InMemoryDbService {
    createDb(reqInfo?: RequestInfo | undefined): {} | Observable<{}> {
        return {
            users: [
                { id: 1, name: '<PERSON>' },
                { id: 2, name: '<PERSON>' },
            ],
            currentUser: {
                dsGuid: '1234567890',
                idIdentity: 1,
                idFirm: 1,
                dsIdentity: '<PERSON>',
                dsMobilePhone: '5555555555',
                dsEmail: '<EMAIL>',
                idIdentityType: 1,
            },
            exchangeRates: [
                {
                    currency: 'EUR',
                    icon: 'eu',
                    sellingRate: 33.090,
                    buyingRate: 33.090,
                    rateChange: 0.01,
                    rateChangeType: 'increase',
                },
                {
                    currency: 'USD',
                    icon: 'us',
                    sellingRate: 33.090,
                    buyingRate: 33.090,
                    rateChange: 0.01,
                    rateChangeType: 'increase',
                }
            ],
            dailyIncomeAndSales: {
                income: 34125.90,
                sales: 77445.00,
                incomeChange: 2,
                incomeChangeType: 'decrease',
                salesChange: 14,
                salesChangeType: 'increase',
                incomeSeries: [1, 11, 22, - 5, 55, 1, 0, 0, -5, 14],
                salesSeries: [-999, -9, 60, 90, -20, 89, 1000, 88, 858, 2, 2323, 2, 2, 434, 54, 65, 67, 76, 999, 666, 100, -8],
                currency: '₺',
            },
            totalNetValue: {
                debt: 156000,
                debtChange: 2,
                debtChangeType: 'decrease',
                stock: 830000,
                stockChange: 0,
                stockChangeType: 'decrease',
                debtSeries: [
                    { label: 'Borç 1', value: 100000, percentage: 100 },
                    { label: 'Borç 2', value: 50000, percentage: 50 },
                    { label: 'Borç 3', value: 30000, percentage: 33 },
                    { label: 'Borç 4', value: 20000, percentage: 80 },
                    { label: 'Borç 5', value: 10000, percentage: 1 },
                ],
                stockSeries: [
                    { label: 'Varlık 1', value: 500000, percentage: 50 },
                    { label: 'Varlık 2', value: 300000, percentage: 30 },
                    { label: 'Varlık 3', value: 200000, percentage: 20 },
                    { label: 'Varlık 4', value: 0, percentage: 0 },
                ]
            },
            upcomingPayments: [
                { label: 'Kredi Kartları', value: 40000, currency: '₺', link: '/payments', colorHex: '#043A82' },
                { label: 'Krediler', value: 15000, currency: '₺', colorHex: '#0559C7' },
                { label: 'Açık Hesap', value: 7000, currency: '₺', colorHex: '#65A5F9' },
                { label: 'Çekler', value: 5000, currency: '₺', colorHex: '#BFD9FD' },
            ],
            overduePayments: [
                { label: 'Açık Hesap', value: 30000, currency: '₺', link: '/payments', colorHex: '#A15E06' },
                { label: 'Kartlar', value: 25000, currency: '₺', colorHex: '#DE8208' },
                { label: 'Masraflar', value: 20000, currency: '₺', colorHex: '#F8A12E' },
                { label: 'Çekler', value: 10000, currency: '₺', colorHex: '#FCCD8F' },
            ],
            invoices: [
                { title: 'Yılmaz Tekstil', value: 21480, currency: '₺', date: new Date(), avatar: 'YT', link: '/invoices' },
                { title: 'Öztürk Ticaret', value: 21480, currency: '₺', date: new Date(), avatar: 'ÖT' },
                { title: 'Demir Sanayi', value: 21480, currency: '₺', date: new Date(), logoUrl: 'https://i.pravatar.cc/32' },
                { title: 'Bulut Gıda', value: 34500, currency: '₺', date: new Date(), avatar: 'BG' },
            ],
            costs: [
                { title: 'Yılmaz Tekstil', value: 21480, currency: '₺', avatar: 'YT', link: '/costs' },
                { title: 'Öztürk Ticaret', value: 21480, currency: '₺', avatar: 'ÖT' },
                { title: 'Demir Sanayi', value: 21480, currency: '₺', logoUrl: 'https://i.pravatar.cc/32' },
                { title: 'Bulut Gıda', value: 34500, currency: '₺', avatar: 'BG' },
            ],
            cheques: [
                { title: 'Yılmaz Tekstil', value: 21480, currency: '₺', avatar: 'YT', link: '/cheques' },
                { title: 'Öztürk Ticaret', value: 21480, currency: '₺', avatar: 'ÖT' },
                { title: 'Demir Sanayi', value: 21480, currency: '₺', logoUrl: 'https://i.pravatar.cc/32' },
                { title: 'Bulut Gıda', value: 34500, currency: '₺', avatar: 'BG' },
            ],
            bonds: [
                { title: 'Yılmaz Tekstil', value: 21480, currency: '₺', avatar: 'YT', link: '/bonds' },
                { title: 'Öztürk Ticaret', value: 21480, currency: '₺', avatar: 'ÖT' },
                { title: 'Demir Sanayi', value: 21480, currency: '₺', logoUrl: 'https://i.pravatar.cc/32' },
                { title: 'Bulut Gıda', value: 34500, currency: '₺', avatar: 'BG' },
            ],
            credits: [
                { title: 'Yılmaz Tekstil', value: 21480, currency: '₺', avatar: 'YT', link: '/credits' },
                //{ title: 'Öztürk Ticaret', value: 21480, currency: '₺', avatar: 'ÖT' },
                { title: 'Demir Sanayi', value: 21480, currency: '₺', logoUrl: 'https://i.pravatar.cc/32' },
                { title: 'Bulut Gıda', value: 34500, currency: '₺', avatar: 'BG' },
            ],
            giro: {
                series: [20000, 30000, 40000, 120000, 50000, 110000, 70000, 10000, 80000, 90000, 100000, 60000],
                currency: '₺',
            }
        };
    }

    get(reqInfo: RequestInfo) {
        // Only handle API requests, let others pass through
        if (!reqInfo.req.url.includes('api/') || reqInfo.req.url.includes('api/v2/customer'))
            return undefined; // Pass through to real backend or assets

        let delayMs = 0;
        const url = reqInfo.req.url;

        if (url.endsWith('/exchangeRates')) {
            delayMs = 3000;
        } else if (url.endsWith('/dailyIncomeAndSales')) {
            delayMs = 1500;
        } else if (url.endsWith('/totalNetValues')) {
            delayMs = 4100;
        } else if (url.endsWith('/upcomingPayments')) {
            delayMs = 2000;
        } else if (url.endsWith('/overduePayments')) {
            delayMs = 5500;
        } else if (url.endsWith('/invoices')) {
            delayMs = 5000;
        } else if (url.endsWith('/costs')) {
            delayMs = 2000;
        } else if (url.endsWith('/cheques')) {
            delayMs = 1500;
        } else if (url.endsWith('/bonds')) {
            delayMs = 4600;
        } else if (url.endsWith('/credits')) {
            delayMs = 3500;
        } else if (url.endsWith('/giro')) {
            delayMs = 2780;
        } else if (url.endsWith('/currentUser')) {
            // delayMs = 1000;
            return undefined;
        }

        return reqInfo.utils.createResponse$(() => {
            return {
                body: reqInfo.collection,
                status: STATUS.OK,
                headers: reqInfo.headers,
                url: reqInfo.url
            };
        }).pipe(delay(delayMs));
    }
}