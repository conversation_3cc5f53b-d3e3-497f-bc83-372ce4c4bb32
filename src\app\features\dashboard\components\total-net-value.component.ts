
import { Component, inject, model, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TooltipModule } from 'primeng/tooltip';
import { TotalNetValueItemModel } from '../models/total-net-value-item.model';
import { RouterModule } from '@angular/router';
import { BaseDashboardComponent } from './base-dashboard.component';
import { DashboardDataService } from '../services/dashboard-data.service';

@Component({
    selector: 'bh-total-net-value',
    templateUrl: './total-net-value.component.html',
    imports: [CommonModule, TooltipModule, RouterModule],
    standalone: true,
    providers: [DashboardDataService],
})
export class TotalNetValueComponent extends BaseDashboardComponent implements OnInit {

    dashboardDataService: DashboardDataService = inject(DashboardDataService);

    override loadData(): void {
        if (this.debt() !== undefined) return;

        this.loading.set(true);

        this.dashboardDataService.getTotalNetValue().subscribe((data) => {
            this.debt.set(data.totalDebt);
            this.debtChange.set(data.debtChange);
            this.stock.set(data.totalAsset);
            this.stockChange.set(data.assetChange);
            this.debtSeries.set(data.debtSeries);
            this.stockSeries.set(data.assetSeries);
            this.currency.set(data.currency);
            this.loading.set(false);
        });
    }

    ngOnInit(): void {
        this.loadData();
    }

    currency = model<string>('TL');
    debt = model<number>();
    debtChange = model<number>();

    stock = model<number>();
    stockChange = model<number>();

    debtSeries = model<TotalNetValueItemModel[]>([]);
    stockSeries = model<TotalNetValueItemModel[]>([]);

    get total() {
        if (this.debt() === undefined || this.stock() === undefined) return 0;
        return this.stock()! - this.debt()!;
    }

    getAbsValue(value?: number) {
        if (!value) return 0;
        return Math.abs(value);
    }
}
