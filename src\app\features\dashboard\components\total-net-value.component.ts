
import { Component, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TooltipModule } from 'primeng/tooltip';
import { TotalNetValueItemModel } from '../models/total-net-value-item.model';
import { RouterModule } from '@angular/router';

@Component({
    selector: 'bh-total-net-value',
    templateUrl: './total-net-value.component.html',
    imports: [CommonModule, TooltipModule, RouterModule],
    standalone: true,
})
export class TotalNetValueComponent {

    currency = input<string>('TL');
    debt = input<number>();
    debtChange = input<number>();
    debtChangeType = input<'increase' | 'decrease'>();

    stock = input<number>();
    stockChange = input<number>();
    stockChangeType = input<'increase' | 'decrease'>();

    debtSeries = input<TotalNetValueItemModel[]>([]);
    stockSeries = input<TotalNetValueItemModel[]>([]);

    get total() {
        return this.stock()! - this.debt()!;
    }
}
