import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { DrawerModule } from 'primeng/drawer';
import { IconComponent } from '../../../core/components/icon.component';
import { DashboardService } from '../services/dashboard.service';
import { DragDropModule } from '@angular/cdk/drag-drop';

@Component({
  selector: 'bh-add-widget',
  templateUrl: './add-widget.component.html',
  standalone: true,
  imports: [CommonModule,
    ButtonModule,
    IconComponent,
    DrawerModule,
    DragDropModule,
  ]
})
export class AddWidgetComponent {

  dashboardService = inject(DashboardService);
  showDrawer = false;

  get availableWidgets() {
    return this.dashboardService.getAvailableWidgets();
  }

  get fullWidthWidgets() {
    return this.availableWidgets.filter(w => w.width === 'full');
  }
  get wideWidthWidgets() {
    return this.availableWidgets.filter(w => w.width === 'wide');
  }
  get narrowWidthWidgets() {
    return this.availableWidgets.filter(w => w.width === 'narrow');
  }

  addWidget(selectedWidget?: any): void {
    if (!selectedWidget) return;

    const newWidget = {
      id: `${selectedWidget.id.split('_')[0]}_${Date.now()}`,
      title: selectedWidget.title,
      componentType: selectedWidget.componentType,
      isStatic: selectedWidget.isStatic,
      width: selectedWidget.width,
      show: true,
      dropZone: selectedWidget.width,
      order: 999 // Will be placed at the end
    };

    this.dashboardService.addComponent(newWidget);
    this.showDrawer = false;
  }
}