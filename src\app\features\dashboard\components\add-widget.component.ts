import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { DrawerModule } from 'primeng/drawer';
import { IconComponent } from '../../../core/components/icon.component';
import { DashboardService } from '../services/dashboard.service';
import { DragDropModule } from '@angular/cdk/drag-drop';

@Component({
  selector: 'bh-add-widget',
  templateUrl: './add-widget.component.html',
  standalone: true,
  imports: [CommonModule,
    ButtonModule,
    IconComponent,
    DrawerModule,
    DragDropModule,
  ]
})
export class AddWidgetComponent {

  dashboardService = inject(DashboardService);
  showDrawer = false;

  get availableWidgets() {
    return this.dashboardService.getAvailableWidgets();
  }

  addWidget(selectedWidget?: any): void {
    if (!selectedWidget) return;

    const newWidget = {
      id: `${selectedWidget.id}-${Date.now()}`,
      title: selectedWidget.title,
      componentType: selectedWidget.componentType,
      isStatic: false,
      width: selectedWidget.width,
      show: true,
      order: 999 // Will be placed at the end
    };

    this.dashboardService.addComponent(newWidget);
    this.showDrawer = false;
  }
}