
import { Component, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ExchangeBarComponent } from "./exchange-bar.component";
import { ExchangeRateModel } from '../models/exchange-rate.model';
import { GradientLineComponent } from '../../../core/components/gradient-line.component';


@Component({
    selector: 'bh-daily-sales-and-income',
    templateUrl: './daily-sales-and-income.component.html',
    imports: [CommonModule, ExchangeBarComponent, GradientLineComponent],
    standalone: true,
})
export class DailySalesAndIncomeComponent {

    currentDateString = new Date().toLocaleDateString('tr-TR', { year: 'numeric', month: 'long', day: 'numeric' });

    exchangeRates = input<ExchangeRateModel[]>();
    income = input<number>();
    incomeChange = input<number>();
    incomeChangeType = input<'increase' | 'decrease'>();

    sales = input<number>();
    salesChange = input<number>();
    salesChangeType = input<'increase' | 'decrease'>();

    salesSeries = input<number[]>([]);
    incomeSeries = input<number[]>([]);

}


