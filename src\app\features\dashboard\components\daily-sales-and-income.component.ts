
import { Component, inject, input, model, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ExchangeBarComponent } from "./exchange-bar.component";
import { ExchangeRateModel } from '../models/exchange-rate.model';
import { GradientLineComponent } from '../../../core/components/gradient-line.component';
import { BaseDashboardComponent } from './base-dashboard.component';
import { DashboardDataService } from '../services/dashboard-data.service';


@Component({
    selector: 'bh-daily-sales-and-income',
    templateUrl: './daily-sales-and-income.component.html',
    imports: [CommonModule, ExchangeBarComponent, GradientLineComponent],
    standalone: true,
    providers: [DashboardDataService],
})
export class DailySalesAndIncomeComponent extends BaseDashboardComponent implements OnInit {

    dashboardDataService: DashboardDataService = inject(DashboardDataService);

    override loadData(): void {
        if (this.income() !== undefined) return;

        this.loading.set(true);
        this.dashboardDataService.getDailyIncomeAndSales().subscribe((data) => {
            this.income.set(data.income);
            this.incomeChange.set(data.incomeChange);
            this.incomeChangeType.set(data.incomeChangeType);
            this.sales.set(data.sales);
            this.salesChange.set(data.salesChange);
            this.salesChangeType.set(data.salesChangeType);
            this.salesSeries.set(data.salesSeries);
            this.incomeSeries.set(data.incomeSeries);
            this.currency.set(data.currency);
            this.loading.set(false);
        });
    }

    ngOnInit(): void {
        this.loadData();
    }

    currentDateString = new Date().toLocaleDateString('tr-TR', { year: 'numeric', month: 'long', day: 'numeric' });

    exchangeRates = model<ExchangeRateModel[]>();

    income = model<number>();
    incomeChange = model<number>();
    incomeChangeType = model<'increase' | 'decrease'>();

    sales = model<number>();
    salesChange = model<number>();
    salesChangeType = model<'increase' | 'decrease'>();

    salesSeries = model<number[]>([]);
    incomeSeries = model<number[]>([]);

    currency = model<string>('₺');

    getAbsValue(value?: number) {
        if (!value) return 0;
        return Math.abs(value);
    }
}


