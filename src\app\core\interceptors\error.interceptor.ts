import { inject } from '@angular/core';
import { HttpRequest, HttpEvent, HttpErrorResponse, HttpHandlerFn } from '@angular/common/http';
import { Observable } from 'rxjs/internal/Observable';
import { catchError } from 'rxjs/internal/operators/catchError';
import { throwError } from 'rxjs/internal/observable/throwError';
import { Router } from '@angular/router';
import { ToastService } from '../services/application/toast.service';
import { SplashScreenService } from '../services/application/splash-screen.service';

export const errorInterceptor = (
    request: HttpRequest<unknown>,
    next: HttpHandlerFn
): Observable<HttpEvent<unknown>> => {
    const alertService = inject(ToastService);
    const splashScreenService = inject(SplashScreenService);
    const router = inject(Router);

    return next(request).pipe(catchError(error => {

        console.log(error);

        if (!request.headers.has("skipInterceptor")) {
            const errorText = error.error?.message || error.error?.messages || error.message || error.statusText;

            if (error instanceof HttpErrorResponse) {

                if (error.error instanceof ErrorEvent) {
                    alertService.error(errorText);
                }
                else {
                    switch (error.status) {
                        case 400: // BadRequest
                            alertService.error(errorText);
                            break;

                        case 401: // Authentication problem
                        case 430: // Token Expired
                        case 440: // Token Invalid
                            if (error.url?.endsWith("authentication/authenticate"))
                                alertService.error(errorText);
                            else
                                router.navigate(['/sign-out']);
                            break;

                        case 403: // Authorization problem
                            window.location.href = "/access-denied";
                            break;

                        case 404: // Exception on API
                            if (error.error)
                                alertService.error(error.error);
                            else
                                alertService.error(`${error.status} : ${error.message}`);
                            break;

                        case 429:
                            alertService.error("İşlem limiti aşıldı! Lütfen daha sonra tekrar deneyin.");
                            break;

                        case 500: // Exception on API
                            alertService.error("İşleminiz sırasında bir hata oluştu. Lütfen daha sonra tekrar deneyin.");
                            // router.navigate(['/internal-error']);
                            break;

                        case -1: // API connection problem
                        case 0:  // Internet connection problem
                            alertService.error("BizimHesap servislerine erişilemiyor. Lütfen internet bağlantınızı kontrol edin.");
                            break;

                        default:
                            alertService.error([error.status, ...errorText].join("<br>"));
                            break;
                    }
                }
            }
            else {
                alertService.error(errorText);
            }
        }

        splashScreenService.hide();

        return throwError(() => error);
    }));
};
