<div class="flex flex-col gap-4 @container">

    <div class="flex gap-3 items-center pt-4">
        <h4>Güncel Durum</h4>

        <p-button class="@max-md:hidden" styleClass="p-button-xs" [disabled]="editMode" variant="text"
            (click)="toggleEditMode()">
            <bh-icon name="edit" [size]="16"></bh-icon>
        </p-button>

        <p-button styleClass="p-button-xs" variant="text" [disabled]="editMode">
            <bh-icon name="eye" [size]="16"></bh-icon>
        </p-button>

        <div #quickMenuContainer class="@max-md:hidden flex justify-items-center ml-auto">
            <p-menu #quickMenu [model]="quickMenuItems" title="Hızlı İşlem" [popup]="true" appendTo="body">
                <ng-template #item let-item>
                    <div
                        class="p-3 flex gap-1 items-center text-sm leading-5 text-coolgray-800 hover:text-coolgray-1000 cursor-pointer">
                        <bh-icon [name]="item.icon" [size]="16"></bh-icon>
                        <span>{{item.label}}</span>
                    </div>
                </ng-template>
            </p-menu>
            <p-button variant="outlined" (click)="quickMenu.toggle($event)" [disabled]="editMode">
                <span>Hızlı İşlem</span>
                <bh-icon name="plus" [size]="16"></bh-icon>
            </p-button>
        </div>
    </div>

    <div class="grid grid-cols-3 gap-4" cdkDropList (cdkDropListDropped)="onDrop($event)" #dashboardList="cdkDropList"
        id="dashboardList" [cdkDropListDisabled]="!editMode">
        @for (component of dashboardComponents; track component.id) {
        @if (!component.showLater) {
        <div cdkDrag [cdkDragDisabled]="!editMode || component.isStatic"
            [ngClass]="{'col-span-3': component.width === 'full', 'col-span-2': component.width === 'wide', 'col-span-1': component.width === 'narrow'}"
            class="mb-2 relative @max-md:col-span-3">

            <div [ngClass]="{
                'opacity-50': component.show == false, 
                'opacity-100': component.show == true, 
                'rounded-lg p-4 bg-white': component.isStatic != true, 
                'min-w-180': component.componentType == 'total-net-value'
                }">
                @switch (component.componentType) {
                @case ('onboarding') {
                <bh-onboarding [activeStepId]="6" (showLater)="showLaterClicked(component)"></bh-onboarding>
                }
                @case ('daily-sales') {
                <bh-daily-sales-and-income [sales]="77445.00" [salesChange]="14" [salesChangeType]="'increase'"
                    [income]="34125.90" [incomeChange]="2" [incomeChangeType]="'decrease'"
                    [exchangeRates]="exchangeRates"
                    [salesSeries]="[-999, -9, 60, 90, -20, 89, 1000, 88, 858, 2, 2323, 2, 2, 434, 54, 65, 67, 76, 999, 666, 100, -80]"
                    [incomeSeries]="[1, 11, 22, - 5, 55, 1, 0, 0, -5, 14]" />
                }
                @case ('total-net-value') {
                <bh-total-net-value [debt]="156000" [debtChange]="2" [debtChangeType]="'decrease'" [stock]="830000"
                    [stockChange]="0" [currency]="'₺'" [stockChangeType]="'decrease'" [debtSeries]="[
                        { label: 'Borç 1', value: 100000, percentage: 100 },
                        { label: 'Borç 2', value: 50000, percentage: 50 },
                        { label: 'Borç 3', value: 30000, percentage: 33 },
                        { label: 'Borç 4', value: 20000, percentage: 80 },
                        { label: 'Borç 5', value: 10000, percentage: 1 },
                    ]" [stockSeries]="[
                        { label: 'Varlık 1', value: 500000, percentage: 50 },
                        { label: 'Varlık 2', value: 300000, percentage: 30 },
                        { label: 'Varlık 3', value: 200000, percentage: 20 },
                        { label: 'Varlık 4', value: 0, percentage: 0 },
                    ]" />
                }
                @case ('invite-accountant') {
                <bh-invite-accountant></bh-invite-accountant>
                }
                @case ('upcoming-payments') {
                <bh-upcoming-payments [upcomingItems]="[
                        { label: 'Kredi Kartları', value: 40000, currency: '₺', link: '/payments', colorHex: '#043A82' },
                        { label: 'Krediler', value: 15000, currency: '₺', colorHex: '#0559C7' },
                        { label: 'Açık Hesap', value: 7000, currency: '₺', colorHex: '#65A5F9' },
                        { label: 'Çekler', value: 5000, currency: '₺', colorHex: '#BFD9FD' },
                ]"></bh-upcoming-payments>
                }
                @case ('overdue-payments') {
                <bh-overdue-payments [overdueItems]="[
                        { label: 'Açık Hesap', value: 30000, currency: '₺', link: '/payments', colorHex: '#A15E06' },
                        { label: 'Kartlar', value: 25000, currency: '₺', colorHex: '#DE8208' },
                        { label: 'Masraflar', value: 20000, currency: '₺', colorHex: '#F8A12E' },
                        { label: 'Çekler', value: 10000, currency: '₺', colorHex: '#FCCD8F' },
                ]"></bh-overdue-payments>
                }
                @case ('incoming-invoices') {
                <bh-costs [costItems]="[
                        { title: 'Yılmaz Tekstil', value: 21480, currency: '₺', date: currentDate, avatar: 'YT', link: '/invoices' },
                        { title: 'Öztürk Ticaret', value: 21480, currency: '₺', date: currentDate, avatar: 'ÖT' },
                        { title: 'Demir Sanayi', value: 21480, currency: '₺', date: currentDate, logoUrl: 'https://i.pravatar.cc/32' },
                        { title: 'Bulut Gıda', value: 34500, currency: '₺', date: yesterdayDate,  avatar: 'BG' },
                    ]" [title]="'Gelen E-Faturalar'"
                    [emptyPlaceholderText]="'Gelen faturalarınızı takip etmek için e-Fatura entegrasyonunuzu tamamlayın.'"
                    [emptyButtonLabel]="'e-Faturamı Bağla'" [emptyButtonLink]="'/e-invoice-application'">
                </bh-costs>
                }
                @case ('costs') {
                <bh-costs [costItems]="[
                        { title: 'Yılmaz Tekstil', value: 21480, currency: '₺', avatar: 'YT', link: '/costs' },
                        { title: 'Öztürk Ticaret', value: 21480, currency: '₺', avatar: 'ÖT' },
                        { title: 'Demir Sanayi', value: 21480, currency: '₺', logoUrl: 'https://i.pravatar.cc/32' },
                        { title: 'Bulut Gıda', value: 34500, currency: '₺', avatar: 'BG' },
                    ]" [tooltip]="'Vadesi geçen 3 adet masrafınız var.'" [tooltipLink]="'/costs'" [title]="'Masraflar'"
                    [emptyPlaceholderText]="'Tüm harcamalarınızı takip etmek için masraf kalemlerinizi ekleyin.'"
                    [emptyButtonLabel]="'Masraf Kalemlerimi Ekle'" [emptyButtonLink]="'/costs'">
                </bh-costs>
                }
                @case ('cheques') {
                <bh-costs [costItems]="[
                        { title: 'Yılmaz Tekstil', value: 21480, currency: '₺', avatar: 'YT', link: '/cheques' },
                        { title: 'Öztürk Ticaret', value: 21480, currency: '₺', avatar: 'ÖT' },
                        { title: 'Demir Sanayi', value: 21480, currency: '₺', logoUrl: 'https://i.pravatar.cc/32' },
                        { title: 'Bulut Gıda', value: 34500, currency: '₺',  avatar: 'BG' },
                    ]" [tooltip]="'Vadesi geçen 3 adet çekiniz var.'" [tooltipLink]="'/cheques'" [title]="'Çekler'"
                    [emptyPlaceholderText]="'Size yazılan veya yazdığınız çeklerin durumunu takip etmek için çeklerinizi ekleyin.'"
                    [emptyButtonLabel]="'Çek Ekle'" [emptyButtonLink]="'/cheques'">
                </bh-costs>
                }
                @case ('bonds') {
                <bh-costs [costItems]="[
                        { title: 'Yılmaz Tekstil', value: 21480, currency: '₺', avatar: 'YT', link: '/bonds' },
                        { title: 'Öztürk Ticaret', value: 21480, currency: '₺', avatar: 'ÖT' },
                        { title: 'Demir Sanayi', value: 21480, currency: '₺',  logoUrl: 'https://i.pravatar.cc/32' },
                        { title: 'Bulut Gıda', value: 34500, currency: '₺',  avatar: 'BG' },
                    ]" [tooltip]="'Vadesi geçen 3 senetiniz var.'" [tooltipLink]="'/bonds'" [title]="'Senetler'"
                    [emptyPlaceholderText]="'Tüm senetlerinizi takip etmek için senetlerinizi ekleyin.'"
                    [emptyButtonLabel]="'Senet Ekle'" [emptyButtonLink]="'/bonds'">
                </bh-costs>
                }
                @case ('credits') {
                <bh-costs [costItems]="[
                        { title: 'Yılmaz Tekstil', value: 21480, currency: '₺', avatar: 'YT', link: '/credits' },
                        { title: 'Öztürk Ticaret', value: 21480, currency: '₺', avatar: 'ÖT' },
                        { title: 'Demir Sanayi', value: 21480, currency: '₺', logoUrl: 'https://i.pravatar.cc/32' },
                        { title: 'Bulut Gıda', value: 34500, currency: '₺',  avatar: 'BG' },
                    ]" [tooltip]="'Vadesi geçen 3 krediniz var.'" [tooltipLink]="'/credits'" [title]="'Krediler'"
                    [emptyPlaceholderText]="'Yaklaşan kredilerinizi takip etmek için banka hesabınızı bağlayın.'"
                    [emptyButtonLabel]="'Banka Hesabımı Bağla'" [emptyButtonLink]="'/credits'">
                </bh-costs>
                }
                @case ('giro') {
                <bh-giro-chart [series]="[20000, 30000, 40000, 120000, 50000, 110000, 70000, 10000, 80000, 90000, 100000, 60000
                     ]"></bh-giro-chart>
                }
                }
                @if(editMode && !component.isStatic) {
                <div class="flex items-center gap-3 pt-4 mt-4 border-t border-t-coolgray-200">
                    <p-check-box inputId="show{{component.id}}" [(ngModel)]="component.show"
                        binary="true"></p-check-box>
                    <label for="show{{component.id}}" class="text-sm text-coolgray-800">{{component.show ? 'Gizle' :
                        'Göster'}}</label>
                </div>
                }
            </div>
        </div>
        }

        }
    </div>
</div>

@if (editMode) {
<div
    class="flex justify-center gap-4 bg-white/80 fixed bottom-0 w-full right-0 p-4 z-10 backdrop-blur-xs shadow-[0px_-1px_10px_var(--color-coolgray-200)]">
    <p-button (click)="toggleEditMode(true)" size="large" variant="text" label="İptal Et" styleClass="mr-32">
    </p-button>
    <bh-add-widget></bh-add-widget>
    <p-button (click)="toggleEditMode()" size="large" label="Kaydet">
    </p-button>
</div>
}