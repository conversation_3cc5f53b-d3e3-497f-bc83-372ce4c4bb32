<div class="flex flex-col gap-4 @container" [ngClass]="{'pb-24': editMode}">

    <div class="flex gap-3 items-center justify-between pt-4">
        <div class="flex gap-3 items-center">
            <h4>Güncel Durum</h4>

            <bh-icon-button name="edit" size="xs" variant="plain" [disabled]="editMode" (click)="toggleEditMode()"
                class="@max-md:hidden!" />
            <bh-icon-button [name]="showValues() ? 'eye' : 'eye-hidden'" size="xs" variant="plain" [disabled]="editMode"
                (click)="toggleShowValue()" />
        </div>

        <bh-remaining-einvoice-counter [counter]="50" />

        <div #quickMenuContainer class="@max-md:hidden flex justify-items-center">
            <p-menu #quickMenu [model]="quickMenuItems" title="<PERSON>ı<PERSON>lı İşlem" [popup]="true" appendTo="body">
                <ng-template #item let-item>
                    <div
                        class="p-2 flex gap-1 items-center text-sm leading-5 text-coolgray-800 hover:text-coolgray-1000 cursor-pointer">
                        <div class="size-5 flex items-center justify-center">
                            <bh-icon [name]="item.icon" [size]="16"></bh-icon>
                        </div>
                        <span>{{item.label}}</span>
                    </div>
                </ng-template>
            </p-menu>

            <bh-button variant="outlined" rightIcon="plus" (click)="quickMenu.toggle($event)" [disabled]="editMode">
                <span>Hızlı İşlem</span>
            </bh-button>
        </div>
    </div>

    <div class="grid grid-cols-3 gap-4">

        <div class="flex flex-col gap-4 col-span-3" cdkDropList (cdkDropListDropped)="onDrop($event)"
            #fullDasboardList="cdkDropList" id="fullDasboardList" [cdkDropListDisabled]="!editMode">
            @for (component of fullWidthComponents(); track component.id) {
            @if (!component.showLater) {
            <div cdkDrag [cdkDragData]="component" [cdkDragDisabled]="!editMode || component.isStatic"
                class="mb-2 relative">
                <div [ngClass]="{
                    'opacity-50': component.show == false, 
                    'opacity-100': component.show == true, 
                    'rounded-lg p-4 bg-white': component.isStatic != true, 
                    }">
                    @switch (component.componentType) {
                    @case ('onboarding') {
                    <bh-onboarding [completedStepIds]="[1,2,3,5,7]"
                        (onShowLaterClicked)="showLaterClicked(component)"></bh-onboarding>
                    }
                    }
                    @if(editMode && !component.isStatic) {
                    <ng-container *ngTemplateOutlet="showHideTemplate; context: {data: component}"></ng-container>
                    }
                </div>
            </div>
            }
            }
        </div>

        <div class="grid grid-cols-2 grid-rows-[max-content] h-fit gap-4 col-span-2 @max-md:col-span-3" cdkDropList
            [cdkDropListConnectedTo]="['narrowDasboardList']" (cdkDropListDropped)="onDrop($event)"
            #wideDasboardList="cdkDropList" id="wideDasboardList" [cdkDropListDisabled]="!editMode">
            @for (component of wideWidthComponents(); track component.id) {
            <div cdkDrag [cdkDragData]="component" [cdkDragDisabled]="!editMode || component.isStatic"
                class="mb-2 @max-md:col-span-2" [ngClass]="
                {
                'col-span-1': component.width == 'narrow',
                'col-span-2': component.width == 'wide'
                }" [cdkDragBoundary]="component.width === 'wide' ? '#wideDasboardList' : ''">
                <div [ngClass]="{
                'opacity-50': component.show == false, 
                'opacity-100': component.show == true, 
                'rounded-lg p-4 bg-white': component.isStatic != true, 
                'min-w-165': component.componentType == 'total-net-value',
                'h-full': !editMode
                }">
                    @switch (component.componentType) {
                    @case ('daily-sales') {
                    <bh-daily-sales-and-income [showValues]="showValues()" />
                    }
                    @case ('total-net-value') {
                    <bh-total-net-value [showValues]="showValues()" />
                    }
                    @case ('invite-accountant') {
                    <bh-invite-accountant [isEditMode]="editMode"
                        (hideClicked)="component.show = false"></bh-invite-accountant>
                    }
                    @case('giro') {
                    <bh-giro-chart [showValues]="showValues()" />
                    }
                    @case ('upcoming-payments') {
                    <bh-upcoming-payments [showValues]="showValues()" />
                    }
                    @case ('overdue-payments') {
                    <bh-overdue-payments [showValues]="showValues()" />
                    }
                    @case ('incoming-invoices') {
                    <bh-costs dataSource="invoices" [title]="'Gelen E-Faturalar'" [showValues]="showValues()"
                        [emptyPlaceholderText]="'Gelen faturalarınızı takip etmek için e-Fatura entegrasyonunuzu tamamlayın.'"
                        [emptyButtonLabel]="'e-Faturamı Bağla'" [emptyButtonLink]="'/e-invoice-application'">
                    </bh-costs>
                    }
                    @case ('costs') {
                    <bh-costs dataSource="costs" [title]="'Masraflar'" [showValues]="showValues()"
                        [emptyPlaceholderText]="'Tüm harcamalarınızı takip etmek için masraf kalemlerinizi ekleyin.'"
                        [emptyButtonLabel]="'Masraf Kalemlerimi Ekle'" [emptyButtonLink]="'/costs'">
                    </bh-costs>
                    }
                    @case ('cheques') {
                    <bh-costs dataSource="cheques" [title]="'Çekler'" [showValues]="showValues()"
                        [emptyPlaceholderText]="'Size yazılan veya yazdığınız çeklerin durumunu takip etmek için çeklerinizi ekleyin.'"
                        [emptyButtonLabel]="'Çek Ekle'" [emptyButtonLink]="'/cheques'">
                    </bh-costs>
                    }
                    @case ('bonds') {
                    <bh-costs dataSource="bonds" [title]="'Senetler'" [showValues]="showValues()"
                        [emptyPlaceholderText]="'Tüm senetlerinizi takip etmek için senetlerinizi ekleyin.'"
                        [emptyButtonLabel]="'Senet Ekle'" [emptyButtonLink]="'/bonds'">
                    </bh-costs>
                    }
                    @case ('credits') {
                    <bh-costs dataSource="credits" [title]="'Krediler'" [showValues]="showValues()"
                        [emptyPlaceholderText]="'Yaklaşan kredilerinizi takip etmek için banka hesabınızı bağlayın.'"
                        [emptyButtonLabel]="'Banka Hesabımı Bağla'" [emptyButtonLink]="'/credits'">
                    </bh-costs>
                    }
                    }
                    @if(editMode && !component.isStatic) {
                    <ng-container *ngTemplateOutlet="showHideTemplate; context: {data: component}"></ng-container>
                    }
                </div>

                <!-- <div *cdkDragPreview class="rounded-lg p-4 bg-white">
                    {{component.title}}
                </div> -->
                <!-- <div class="rounded-lg p-4 bg-white"
                    [ngClass]="{'col-span-2': component.width == 'wide', 'col-span-1': component.width == 'narrow'}"
                    *cdkDragPlaceholder>
                    <span class="text-base font-medium">{{component.title}}</span>
                </div> -->
            </div>
            }
        </div>

        <div class="flex flex-col gap-4 col-span-1 @max-md:col-span-3" cdkDropList
            [cdkDropListConnectedTo]="['wideDasboardList']" (cdkDropListDropped)="onDrop($event)"
            #narrowDasboardList="cdkDropList" id="narrowDasboardList" [cdkDropListDisabled]="!editMode">
            @for (component of narrowWidthComponents(); track component.id) {
            <div cdkDrag [cdkDragData]="component" [cdkDragDisabled]="!editMode || component.isStatic" class="mb-2">
                <div [ngClass]="{
                'opacity-50': component.show == false, 
                'opacity-100': component.show == true, 
                'rounded-lg p-4 bg-white': component.isStatic != true, 
                }">
                    @switch (component.componentType) {
                    @case ('upcoming-payments') {
                    <bh-upcoming-payments [showValues]="showValues()" />
                    }
                    @case ('overdue-payments') {
                    <bh-overdue-payments [showValues]="showValues()" />
                    }
                    @case ('incoming-invoices') {
                    <bh-costs dataSource="invoices" [title]="'Gelen E-Faturalar'" [showValues]="showValues()"
                        [emptyPlaceholderText]="'Gelen faturalarınızı takip etmek için e-Fatura entegrasyonunuzu tamamlayın.'"
                        [emptyButtonLabel]="'e-Faturamı Bağla'" [emptyButtonLink]="'/e-invoice-application'">
                    </bh-costs>
                    }
                    @case ('costs') {
                    <bh-costs dataSource="costs" [title]="'Masraflar'" [showValues]="showValues()"
                        [emptyPlaceholderText]="'Tüm harcamalarınızı takip etmek için masraf kalemlerinizi ekleyin.'"
                        [emptyButtonLabel]="'Masraf Kalemlerimi Ekle'" [emptyButtonLink]="'/costs'">
                    </bh-costs>
                    }
                    @case ('cheques') {
                    <bh-costs dataSource="cheques" [title]="'Çekler'" [showValues]="showValues()"
                        [emptyPlaceholderText]="'Size yazılan veya yazdığınız çeklerin durumunu takip etmek için çeklerinizi ekleyin.'"
                        [emptyButtonLabel]="'Çek Ekle'" [emptyButtonLink]="'/cheques'">
                    </bh-costs>
                    }
                    @case ('bonds') {
                    <bh-costs dataSource="bonds" [title]="'Senetler'" [showValues]="showValues()"
                        [emptyPlaceholderText]="'Tüm senetlerinizi takip etmek için senetlerinizi ekleyin.'"
                        [emptyButtonLabel]="'Senet Ekle'" [emptyButtonLink]="'/bonds'">
                    </bh-costs>
                    }
                    @case ('credits') {
                    <bh-costs dataSource="credits" [title]="'Krediler'" [showValues]="showValues()"
                        [emptyPlaceholderText]="'Yaklaşan kredilerinizi takip etmek için kredilerinizi ekleyin.'"
                        [emptyButtonLabel]="'Kredi Ekle'" [emptyButtonLink]="'/credits'">
                    </bh-costs>
                    }
                    }
                    @if(editMode && !component.isStatic) {
                    <ng-container *ngTemplateOutlet="showHideTemplate; context: {data: component}"></ng-container>
                    }
                </div>

                <!-- <div class="rounded-lg p-4 bg-white col-span-1" *cdkDragPlaceholder>
                    <span class="text-base font-medium">{{component.title}}</span>
                </div> -->
            </div>
            }
        </div>

    </div>
</div>

@if (editMode) {
<div
    class="flex justify-center gap-x-32 bg-white/80 fixed bottom-0 w-full right-0 p-4 z-10 backdrop-blur-xs shadow-[0px_-1px_10px_var(--color-coolgray-200)]">
    <bh-button size="lg" variant="plain" (click)="toggleEditMode(true)">İptal Et</bh-button>

    <div class="flex gap-4">
        <bh-add-widget></bh-add-widget>
        <bh-button size="lg" (click)="toggleEditMode()">Kaydet</bh-button>
    </div>
</div>
}

<ng-template #showHideTemplate let-component="data">
    <div class="flex items-center gap-3 pt-4 mt-4 border-t border-t-coolgray-200">
        <p-check-box inputId="show{{component.id}}" [(ngModel)]="component.show" binary="true"></p-check-box>
        <label for="show{{component.id}}" class="text-sm text-coolgray-800">{{component.show ? 'Gizle' :
            'Göster'}}</label>
    </div>
</ng-template>