import { definePreset } from '@primeng/themes';
import Aura from '@primeng/themes/aura';

const AppPreset = definePreset(Aura, {
    // components: {
    //     button: {
    //         colorScheme: {
    //             root: {
    //                 primary: {
    //                     color: '#ffffff',
    //                     background: '{primary.1000}',
    //                     borderColor: '{primary.1000}',

    //                     hoverColor: '#ffffff',
    //                     hoverBackground: '{primary.900}',
    //                     hoverBorderColor: '{primary.900}',
                        
    //                     activeColor: '#ffffff',
    //                     activeBackground: '{primary.800}',
    //                     activeBorderColor: '{primary.800}',

    //                     focusRing: {
    //                         color: '{primary.1000}',
    //                         shadow: 'none'
    //                     }
    //                 },
    //                 secondary: {
    //                     color: '#ffffff',
    //                     background: '{secondary.500}',
    //                     borderColor: '{secondary.500}',

    //                     hoverColor: '#ffffff',
    //                     hoverBackground: '{secondary.600}',
    //                     hoverBorderColor: '{secondary.600}',

    //                     activeColor: '#ffffff',
    //                     activeBackground: '{secondary.700}',
    //                     activeBorderColor: '{secondary.700}',

    //                     focusRing: {
    //                         color: '{secondary.500}',
    //                         shadow: 'none'
    //                     }

    //                 }
    //             }
    //         }
    //     }
    // },
    semantic: {
        primary: {
            50: 'var(--color-coolgray-50)',
            100: 'var(--color-coolgray-100)',
            200: 'var(--color-coolgray-200)',
            300: 'var(--color-coolgray-300)',
            400: 'var(--color-coolgray-400)',
            500: 'var(--color-coolgray-500)',
            600: 'var(--color-coolgray-600)',
            700: 'var(--color-coolgray-700)',
            800: 'var(--color-coolgray-800)',
            900: 'var(--color-coolgray-900)',
            1000: 'var(--color-coolgray-1000)'
        },
        secondary: {
            50: 'var(--color-blue-50)',
            100: 'var(--color-blue-100)',
            200: 'var(--color-blue-200)',
            300: 'var(--color-blue-300)',
            400: 'var(--color-blue-400)',
            500: 'var(--color-blue-500)',
            600: 'var(--color-blue-600)',
            700: 'var(--color-blue-700)',
            800: 'var(--color-blue-800)',
            900: 'var(--color-blue-900)',
            1000: 'var(--color-blue-1000)'
        },
        colorScheme: {
            primary: {
                color: '{primary.1000}',
                inverseColor: '#ffffff',
                hoverColor: '{primary.900}',
                activeColor: '{primary.900}'
            },
            secondary: {
                color: '{secondary.500}',
                inverseColor: '#ffffff',
                hoverColor: '{secondary.700}',
                activeColor: '{secondary.700}'
            },
            // tertiary: {
            //     color: 'var(--color-coolgray-900)',
            //     inverseColor: '#ffffff',
            //     hoverColor: 'var(--color-coolgray-1000)',
            //     activeColor: 'var(--color-coolgray-900)'
            // },
            // highlight: {
            //     background: 'var(--color-coolgray-500)',
            //     focusBackground: 'var(--color-coolgray-700)',
            //     color: '#ffffff',
            //     focusColor: '#ffffff'
            // },
            focusRing: {
                width: '1px',
                style: 'solid',
                // color: '{primary.color}',
                offset: '2px'
            },
            navigation: {
                item: {
                    focusBackground: "var(--color-coolgray-200)",
                    activeBackground: "var(--color-coolgray-200)",
                }
            }
        }
    }
});

export default AppPreset;