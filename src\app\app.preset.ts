import { definePreset } from '@primeng/themes';
import Aura from '@primeng/themes/aura';

const AppPreset = definePreset(Aura, {
    semantic: {
        primary: {
            50: 'var(--color-coolgray-50)',
            100: 'var(--color-coolgray-100)',
            200: 'var(--color-coolgray-200)',
            300: 'var(--color-coolgray-300)',
            400: 'var(--color-coolgray-400)',
            500: 'var(--color-coolgray-500)',
            600: 'var(--color-coolgray-600)',
            700: 'var(--color-coolgray-700)',
            800: 'var(--color-coolgray-800)',
            900: 'var(--color-coolgray-900)',
            950: 'var(--color-coolgray-1000)'
        },
        secondary: {
            50: 'var(--color-blue-50)',
            100: 'var(--color-blue-100)',
            200: 'var(--color-blue-200)',
            300: 'var(--color-blue-300)',
            400: 'var(--color-blue-400)',
            500: 'var(--color-blue-500)',
            600: 'var(--color-blue-600)',
            700: 'var(--color-blue-700)',
            800: 'var(--color-blue-800)',
            900: 'var(--color-blue-900)',
            950: 'var(--color-blue-1000)'
        },
        colorScheme: {
            primary: {
                color: 'var(--color-coolgray-1000)',
                inverseColor: '#ffffff',
                hoverColor: 'var(--color-coolgray-900)',
                activeColor: 'var(--color-coolgray-1000)'
            },
            secondary: {
                color: 'var(--color-blue-500)',
                inverseColor: '#ffffff',
                hoverColor: 'var(--color-blue-700)',
                activeColor: 'var(--color-blue-500)'
            },
            tertiary: {
                color: 'var(--color-coolgray-900)',
                inverseColor: '#ffffff',
                hoverColor: 'var(--color-coolgray-1000)',
                activeColor: 'var(--color-coolgray-900)'
            },
            highlight: {
                background: 'var(--color-coolgray-500)',
                focusBackground: 'var(--color-coolgray-700)',
                color: '#ffffff',
                focusColor: '#ffffff'
            },
            focusRing: {
                width: '1px',
                style: 'solid',
                color: '{primary.color}',
                offset: '2px'
            }
        }
    }
});

export default AppPreset;