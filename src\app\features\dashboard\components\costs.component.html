<div class="flex flex-col gap-4 bg-white">
    <div class="flex justify-between cursor-pointer">
        <span class="text-base font-medium">{{title()}}</span>
        <bh-icon name="chevron-right" [size]="16"></bh-icon>
    </div>

    @if (hasError) {
        <bh-dashboard-error [error]="error()!" (onRetry)="retry()"></bh-dashboard-error>
    }
    @else{

    @if (loading()) {
    <div class="flex flex-col gap-4 py-4">
        @for (item of [1,2,3,4]; track $index) {
        <div class="flex flex-row gap-3 items-center">
            <div class="w-8 h-8 rounded-full bg-coolgray-200"></div>
            <div class="h-3.5 bg-coolgray-200 rounded-sm w-23"></div>
            <div class="ml-auto h-3.5 bg-linear-to-r from-coolgray-200 to-coolgray-50 rounded-sm w-23"></div>
        </div>
        }
    </div>
    }
    @else{
    <div class="flex flex-col gap-3">
        @if(overduedCount()){
        <div class="flex gap-2 items-center py-1 px-2 rounded-full bg-warning-100">
            <div class="w-1 h-1 min-w-1 min-h-1 max-h-1 max-w-1 rounded-full bg-warning-600">
            </div>
            <span class="text-xs text-warning-1100" [innerHTML]="tooltip()"></span>
            @if(tooltipLink()) {
            <a [routerLink]="tooltipLink()" class="ml-auto text-warning-900 cursor-pointer">
                <bh-icon name="chevron-right" [size]="16"></bh-icon>
            </a>
            }
        </div>
        }

        @if(costItems().length === 0) {
        <div class="flex flex-col gap-3 px-8 py-18 items-center text-center">
            <span class="text-coolgray-800 text-sm">{{emptyPlaceholderText()}}</span>
            <p-button variant="text" [label]="emptyButtonLabel()" size="small" [routerLink]="emptyButtonLink()">
            </p-button>
        </div>
        }
        @else {
        @for (item of costItems(); track $index) {
        <div class="flex gap-3 items-center px-1">
            <div
                class="w-8 h-8 rounded-full border border-coolgray-200 text-coolgray-800 bg-coolgray-100 content-center text-center">
                @if(item.logoUrl) {
                <img [src]="item.logoUrl" alt="{{item.title}}" class="w-full h-full rounded-full object-contain" />
                }
                @else {
                <span class="text-sm font-medium">{{item.avatar}}</span>
                }
            </div>
            <span class="text-coolgray-800 text-sm font-medium">{{item.title}}</span>
            <div class="flex flex-col gap-0.5 ml-auto text-right">
                <span class="ml-auto text-coolgray-900 text-sm font-medium">{{showValues() ? (item.value |
                    number:'1.2-2') : '*******'}}
                    {{item.currency}}</span>

                @if(item.date){
                @if (isToday(item.date)) {
                <span class="text-success-700 text-xs">Bugün, {{item.date | date:'HH:mm'}}</span>
                }
                @else {
                <span class="text-coolgray-600 text-xs">{{item.date | date:'dd.MM.yyyy'}}</span>
                }
                }
            </div>
            @if (item.link) {
            <a [routerLink]="item.link">
                <bh-icon name="chevron-right" [size]="16"></bh-icon>
            </a>
            }
        </div>
        }
        }
    </div>
    }
    }

</div>