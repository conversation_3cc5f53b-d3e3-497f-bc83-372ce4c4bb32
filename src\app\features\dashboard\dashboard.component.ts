import { ChangeDetectorRef, Component, computed, inject, OnInit } from '@angular/core';
import { OnboardingComponent } from "./components/onboarding.component";
import { IconComponent } from "../../core/components/icon.component";
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { MenuItem, MessageService } from 'primeng/api';
import { DailySalesAndIncomeComponent } from './components/daily-sales-and-income.component';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { CdkDragDrop, DragDropModule } from '@angular/cdk/drag-drop';
import { DashboardService } from './services/dashboard.service';
import { DashboardComponentModel } from './models/dashboard-component.model';
import { AddWidgetComponent } from './components/add-widget.component';
import { CheckboxModule } from 'primeng/checkbox';
import { FormsModule } from '@angular/forms';
import { TotalNetValueComponent } from './components/total-net-value.component';
import { InviteAccountantComponent } from './components/invite-accountant.component';
import { UpcomingPaymentsComponent } from './components/upcoming-payments.component';
import { OverduePaymentsComponent } from './components/overdue-payments.component';
import { CostsComponent } from './components/costs.component';
import { GiroChartComponent } from './components/giro-chart.component';
import { AlertDialogService } from '../../core/services/application/alert-dialog.service';
import { IconButtonComponent, ButtonComponent } from "../../core/components/button/button.component";
import { ToastModule } from 'primeng/toast';
import { ToastService } from '../../core/services/application/toast.service';
import { RemainingEInvoiceCounterComponent } from './components/remaining-einvoice-counter.component';

@Component({
   selector: 'bh-dashboard',
   templateUrl: './dashboard.component.html',
   imports: [
      CommonModule,
      DragDropModule,
      FormsModule,
      IconComponent,
      MenuModule,
      ButtonModule,
      CheckboxModule,
      AddWidgetComponent,
      OnboardingComponent,
      DailySalesAndIncomeComponent,
      TotalNetValueComponent,
      InviteAccountantComponent,
      UpcomingPaymentsComponent,
      OverduePaymentsComponent,
      CostsComponent,
      GiroChartComponent,
      ToastModule,
      IconButtonComponent,
      ButtonComponent,
      RemainingEInvoiceCounterComponent,
   ],
   standalone: true,
})
export class DashboardComponent implements OnInit {

   alertDialogService = inject(AlertDialogService);

   quickMenuItems: MenuItem[] | undefined;
   router: Router = inject(Router);
   dashboardService = inject(DashboardService);
   toastService = inject(ToastService);

   editMode = false;
   showValues = computed(() => this.dashboardService.showValues());

   sortedDashboardComponents = computed(() =>
      this.dashboardService.dashboardComponents().sort((a, b) => a.order - b.order)
   );

   fullWidthComponents = computed(() =>
      this.dashboardService.dashboardComponents().filter(c => c.dropZone === 'full').sort((a, b) => a.order - b.order)
   );

   wideWidthComponents = computed(() =>
      this.dashboardService.dashboardComponents().filter(c => c.dropZone === 'wide').sort((a, b) => a.order - b.order)
   );

   narrowWidthComponents = computed(() =>
      this.dashboardService.dashboardComponents().filter(c => c.dropZone === 'narrow').sort((a, b) => a.order - b.order)
   );

   ngOnInit() {
      this.quickMenuItems = [
         {
            label: 'Satış Faturası Oluştur',
            icon: 'bell-01',
         },
         {
            label: 'Yeni Masraf Gir',
            icon: 'file-04',
            command: () => {
               this.router.navigate(['/costs']);
            }
         },
         {
            label: 'Müşteri Oluştur',
            icon: 'user-plus-02',
            command: () => {
               this.router.navigate(['/customers']);
            }
         },
      ];

      this.dashboardService.loadUserPreferences();
   }

   async toggleShowValue() {
      this.dashboardService.toggleShowValues();
      // this.toastService.success('Duyuru okundu olarak işaretlendi', 'Geri Al');
      // this.toastService.success('Duyuru okundu olarak işaretlendi');

      // this.alertDialogService.success("Başlangıç adımlarını tamamladınız. Uygulamayı kullanmaya devam edebilirsiniz.", "Artık hazırsınız!", "Devam Et");
   }

   async showOnboardingCompleted() {
      this.alertDialogService.success("Başlangıç adımlarını tamamladınız. Uygulamayı kullanmaya devam edebilirsiniz.", "Artık hazırsınız!", "Devam Et");
   }

   async toggleEditMode(skipSave?: boolean) {
      if (skipSave == true) {
         const dialogResult = await this.alertDialogService.confirm("Yaptığınız tüm değişiklikler silinecek. İşlemi iptal etmek istediğinize emin misiniz?", "İşlemi iptal ediyorsunuz...", "İşlemleri İptal Et", "Düzenlemeye Devam Et",)
         if (!dialogResult) return;

         this.editMode = !this.editMode;
         this.dashboardService.loadUserPreferences();
         return;
      }

      this.editMode = !this.editMode;
      if (!this.editMode) {
         this.dashboardService.removeHiddenComponents();
         this.dashboardService.saveUserPreferences();

         this.toastService.success("Değişiklikler kaydedildi!", undefined, 'bottom-right');
      }
   }

   removeComponent(componentId: string) {
      this.dashboardService.removeComponent(componentId);
   }

   onDrop(event: CdkDragDrop<DashboardComponentModel[]>) {
      let targetZone: 'full' | 'wide' | 'narrow' | undefined;
      if (event.container.id === 'fullDasboardList') targetZone = 'full';
      else if (event.container.id === 'wideDasboardList') targetZone = 'wide';
      else if (event.container.id === 'narrowDasboardList') targetZone = 'narrow';

      const newComponent = event.item.data;

      // Only allow valid drops
      if (
         (targetZone === 'full' && newComponent.width !== 'full') ||
         (targetZone === 'narrow' && newComponent.width !== 'narrow') ||
         (targetZone === 'wide' && !(newComponent.width === 'wide' || newComponent.width === 'narrow'))
      ) {
         // Invalid drop, ignore
         return;
      }

      // Get all rendered components
      const renderedComponents = this.dashboardService.dashboardComponents();

      // Remove from previous zone
      const prevIndex = renderedComponents.findIndex(c => c.id === newComponent.id);
      if (prevIndex > -1) {
         renderedComponents.splice(prevIndex, 1);
      }

      // Set new drop zone property
      newComponent.dropZone = targetZone;

      // Find filtered list for the target zone
      let targetZoneComponents = renderedComponents.filter(c => c.dropZone === targetZone);

      // Insert at new index
      // Calculate the index in the main array where to insert the component
      // Find the index of the component that would be at event.currentIndex in the target zone
      let insertBeforeId: string | undefined = targetZoneComponents[event.currentIndex]?.id;
      let insertIndex: number;

      if (insertBeforeId) {
         insertIndex = renderedComponents.findIndex(c => c.id === insertBeforeId);
      } else {
         // If dropping at the end, insert after the last item in the target zone
         // Find the last index of the target zone components
         let lastTargetIdx = renderedComponents
            .map((c, i) => c.dropZone === targetZone ? i : -1)
            .filter(i => i !== -1)
            .pop() ?? -1;

         insertIndex = lastTargetIdx + 1;
      }

      renderedComponents.splice(insertIndex, 0, newComponent);

      // Update order
      renderedComponents.forEach((c, idx) => c.order = idx);

      // Save new order
      this.dashboardService.reorderComponents(renderedComponents.map(c => c.id));
   }

   showLaterClicked(component: DashboardComponentModel) {
      component.showLater = true;
      this.dashboardService.removeComponent(component.id);
      this.dashboardService.saveUserPreferences();
   }
}
