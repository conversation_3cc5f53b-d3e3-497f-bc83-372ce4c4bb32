import { Component, inject, OnInit } from '@angular/core';
import { OnboardingComponent } from "./components/onboarding.component";
import { IconComponent } from "../../core/components/icon.component";
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { MenuItem } from 'primeng/api';
import { DailySalesAndIncomeComponent } from './components/daily-sales-and-income.component';
import { Router } from '@angular/router';
import { ExchangeRateModel } from './models/exchange-rate.model';
import { CommonModule } from '@angular/common';
import { CdkDragDrop, DragDropModule, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { DashboardService } from './services/dashboard.service';
import { DashboardComponentModel } from './models/dashboard-component.model';
import { AddWidgetComponent } from './components/add-widget.component';
import { CheckboxModule } from 'primeng/checkbox';
import { FormsModule } from '@angular/forms';
import { TotalNetValueComponent } from './components/total-net-value.component';
import { InviteAccountantComponent } from './components/invite-accountant.component';
import { UpcomingPaymentsComponent } from './components/upcoming-payments.component';
import { OverduePaymentsComponent } from './components/overdue-payments.component';
import { CostsComponent } from './components/costs.component';
import { GiroChartComponent } from './components/giro-chart.component';

@Component({
   selector: 'bh-dashboard',
   templateUrl: './dashboard.component.html',
   imports: [
      CommonModule,
      DragDropModule,
      FormsModule,
      IconComponent,
      MenuModule,
      ButtonModule,
      CheckboxModule,
      AddWidgetComponent,
      OnboardingComponent,
      DailySalesAndIncomeComponent,
      TotalNetValueComponent,
      InviteAccountantComponent,
      UpcomingPaymentsComponent,
      OverduePaymentsComponent,
      CostsComponent,
      GiroChartComponent
   ],
})
export class DashboardComponent implements OnInit {

   currentDate = new Date();
   yesterdayDate = new Date(this.currentDate.getTime() - 24 * 60 * 60 * 1000);

   quickMenuItems: MenuItem[] | undefined;
   router: Router = inject(Router);
   dashboardService = inject(DashboardService);
   dashboardComponents: DashboardComponentModel[] = [];
   editMode = false;

   exchangeRates: ExchangeRateModel[] = [
      {
         currency: 'EUR',
         icon: 'eu',
         sellingRate: 1.05,
         buyingRate: 33.090,
      },
      {
         currency: 'USD',
         icon: 'us',
         sellingRate: 1.12,
         buyingRate: 1.10,
      }
   ];

   ngOnInit() {
      this.quickMenuItems = [
         {
            label: 'Satış Faturası Oluştur',
            icon: 'bell',
         },
         {
            label: 'Yeni Masraf Gir',
            icon: 'file-04',
            command: () => {
               this.router.navigate(['/costs']);
            }
         },
         {
            label: 'Müşteri Oluştur',
            icon: 'user-plus-02',
            command: () => {
               this.router.navigate(['/customers']);
            }
         },
      ];

      this.dashboardService.loadUserPreferences();
      this.dashboardService.dashboardComponents$.subscribe(components => {
         this.dashboardComponents = components.sort((a, b) => a.order - b.order);
      });
   }


   toggleEditMode(skipSave?: boolean) {
      this.editMode = !this.editMode;
      if (skipSave == true) {
         this.dashboardService.loadUserPreferences();
         return;
      }
      if (!this.editMode) {
         this.dashboardService.removeHiddenComponents();
         this.dashboardService.saveUserPreferences();
      }
   }

   removeComponent(componentId: string) {
      this.dashboardService.removeComponent(componentId);
   }

   onDrop(event: CdkDragDrop<DashboardComponentModel[]>) {

      if (event.previousContainer.id === event.container.id) {
         if (event.previousIndex !== event.currentIndex) {
            moveItemInArray(this.dashboardComponents, event.previousIndex, event.currentIndex);
            const componentIds = this.dashboardComponents.map(c => c.id);
            this.dashboardService.reorderComponents(componentIds);
         }
      }
      else {
         transferArrayItem([event.item.data],
            this.dashboardComponents,
            event.previousIndex,
            event.currentIndex
         );
         const componentIds = this.dashboardComponents.map(c => c.id);
         this.dashboardService.reorderComponents(componentIds);
      }
   }

   showLaterClicked(component: DashboardComponentModel) {
      component.showLater = true;
      this.dashboardService.removeComponent(component.id);
      this.dashboardService.saveUserPreferences();
   }
}
