<div class="flex-start flex h-2 w-full overflow-hidden rounded-full bg-coolgray-100 min-w-32">
    <div class="flex h-full items-center justify-center overflow-hidden break-all bg-blue-500 text-white"
        [style.width]="progress + '%'" [ngClass]="{ 
            'bg-blue-500': value() < max() && !isIndeterminate(), 
            'bg-success-600': value() === max(),
            'bg-[url(/assets/images/progress_bg.svg)] bg-cover': isIndeterminate()
        }">
    </div>
</div>