
import { HttpClient } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { environment } from '../../../../environments/environment';
import { CurrentUserModel } from './user-data.service.models';
import { first, map, Observable, of } from 'rxjs';
import { ApiHttpService } from '../api-http.service';

@Injectable({
    providedIn: 'root'
})
export class UserDataService {

    currentUserSignal = signal<CurrentUserModel | null>(null);
    currentUser = this.currentUserSignal.asReadonly();

    private httpService = inject(ApiHttpService);

    getCurrentUser(fromCache = false): Observable<CurrentUserModel | null> {
        if (fromCache) {
            const cached = this.currentUserSignal();
            if (cached) return of(cached);
        }

        return this.httpService.get<CurrentUserModel>('/users/currentUser').pipe(
            first(),
            map(user => {
                this.currentUserSignal.set(user ?? null);
                return user ?? null;
            })
        );
    }

}
