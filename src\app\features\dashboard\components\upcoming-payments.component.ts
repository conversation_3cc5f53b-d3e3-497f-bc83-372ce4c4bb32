
import { Component, input, OnInit } from '@angular/core';
import { IconComponent } from "../../../core/components/icon.component";
import { PaymentItemModel } from '../models/payment-item.model';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';

@Component({
    selector: 'bh-upcoming-payments',
    templateUrl: 'upcoming-payments.component.html',
    imports: [IconComponent, CommonModule, RouterModule, ButtonModule]
})
export class UpcomingPaymentsComponent implements OnInit {

    upcomingItems = input<PaymentItemModel[]>([]);

    get totalValue() {
        return this.upcomingItems().reduce((acc, item) => acc + item.value, 0);
    }

    get currency() {
        return this.upcomingItems()[0]?.currency ?? '₺';
    }

    get colorArray() {
        let colorArray: string[] = [];

        if (this.upcomingItems().length === 0) {
            for (let i = 0; i < 20; i++) {
                colorArray.push('#EDF1F7');
            }
        }
        else {
            this.upcomingItems().forEach(x => {
                const ratio = x.value / this.totalValue;
                const steps = Math.ceil(ratio * 20);
                for (let i = 0; i < steps; i++) {
                    colorArray.push(x.colorHex);
                }
            });
        }
        return colorArray;
    }

    constructor() { }

    ngOnInit() { }


}