
import { Component, inject, model, OnInit } from '@angular/core';
import { IconComponent } from "../../../core/components/icon.component";
import { PaymentItemModel } from '../models/payment-item.model';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { BaseDashboardComponent } from './base-dashboard.component';
import { DashboardDataService } from '../services/dashboard-data.service';

@Component({
    selector: 'bh-upcoming-payments',
    templateUrl: 'upcoming-payments.component.html',
    imports: [IconComponent, CommonModule, RouterModule, ButtonModule],
    standalone: true,
    providers: [DashboardDataService],
})
export class UpcomingPaymentsComponent extends BaseDashboardComponent implements OnInit {

    dashboardDataService: DashboardDataService = inject(DashboardDataService);

    override loadData(): void {
        if (this.upcomingItems().length > 0) return;

        this.loading.set(true);

        this.dashboardDataService.getUpcomingPayments().subscribe((data) => {
            this.upcomingItems.set(data);
            this.loading.set(false);
        });
    }

    upcomingItems = model<PaymentItemModel[]>([]);

    get totalValue() {
        return this.upcomingItems().reduce((acc, item) => acc + item.value, 0);
    }

    get currency() {
        return this.upcomingItems()[0]?.currency ?? '₺';
    }

    get colorArray() {
        let colorArray: string[] = [];

        if (this.upcomingItems().length === 0) {
            for (let i = 0; i < 20; i++) {
                colorArray.push('#EDF1F7');
            }
        }
        else {
            this.upcomingItems().forEach(x => {
                const ratio = x.value / this.totalValue;
                const steps = Math.ceil(ratio * 20);
                for (let i = 0; i < steps; i++) {
                    colorArray.push(x.colorHex);
                }
            });
        }
        return colorArray;
    }

    ngOnInit() {
        this.loadData();
    }


}