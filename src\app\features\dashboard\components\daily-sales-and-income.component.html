<div class="flex flex-col gap-4 bg-white">

    <div class="flex justify-between gap-4 @max-md:flex-col">
        <span class="text-base font-medium">
            {{currentDateString}}
        </span>
        <bh-exchange-bar></bh-exchange-bar>
    </div>

    <div class="flex gap-4 @max-md:flex-col">

        <div class="flex-1 flex flex-col rounded-lg bg-coolgray-100 px-3 py-4 min-h-34 justify-between" [ngClass]="{
                'bg-linear-to-r from-coolgray-200 to-coolgray-50': loading()
            }">
            <span class="text-base font-medium">Bugünk<PERSON>ış</span>
            @if(!loading()){
            <div class="flex flex-row gap-2">
                <div class="flex flex-1 flex-col gap-2">
                    <div class="flex gap-1 items-center">
                        <h2>
                            @if(showValues()) {
                            {{sales() | number:'1.2-2'}}
                            }
                            @else {
                            ********
                            }
                        </h2>
                        <h5>{{currency()}}</h5>
                    </div>
                    <div class="min-h-[18px] text-xs flex gap-1 items-center">
                        <span>
                            düne kıyasla
                        </span>
                        @if (salesChange(); as salesChange) {
                        <div class="py-0.5 px-1.5 rounded-full font-medium" [ngClass]="{
                            'bg-lime-300': salesChange >= 0,
                            'bg-red-100': salesChange < 0
                        }">

                            @if(salesChange >= 0) {
                            <span class="text-forest-600">+%{{getAbsValue(salesChange)}}</span>
                            }
                            @else {
                            <span class="text-red-500">-%{{getAbsValue(salesChange)}}</span>
                            }
                        </div>
                        }


                    </div>
                </div>
                <div class="flex flex-1 gap-2 items-center justify-center">
                    <bh-gradient-line [series]="salesSeries()" [width]="120" [height]="40" />
                </div>
            </div>
            }
        </div>

        <div class="flex-1 flex flex-col rounded-lg bg-coolgray-100 px-3 py-4 min-h-34 justify-between" [ngClass]="{
                'bg-linear-to-r from-coolgray-200 to-coolgray-50': loading()
            }">
            <span class="text-base font-medium">Bugünkü Tahsilat</span>
            @if(!loading()){
            <div class="flex flex-row gap-2">
                <div class="flex flex-1 flex-col gap-2">
                    <div class="flex gap-1 items-center">
                        <h2>
                            @if(showValues()) {
                            {{income() | number:'1.2-2'}}
                            }
                            @else {
                            ********
                            }
                        </h2>
                        <h5>{{currency()}}</h5>
                    </div>
                    <div class="min-h-[18px] text-xs flex gap-1 items-center">
                        <span>
                            düne kıyasla
                        </span>
                        @if(incomeChange(); as incomeChange) {
                        <div class="py-0.5 px-1.5 rounded-full font-medium" [ngClass]="{
                            'bg-lime-300': incomeChange >= 0,
                            'bg-red-100': incomeChange < 0
                        }">

                            @if(incomeChange >= 0) {
                            <span class="text-forest-600">+%{{getAbsValue(incomeChange)}}</span>
                            }
                            @else {
                            <span class="text-red-500">-%{{getAbsValue(incomeChange)}}</span>
                            }
                        </div>
                        }
                    </div>
                </div>
                <div class="flex flex-1 gap-2 items-center justify-center">
                    <bh-gradient-line [series]="incomeSeries()" [width]="120" [height]="40" />
                </div>
            </div>
            }
        </div>

    </div>
</div>