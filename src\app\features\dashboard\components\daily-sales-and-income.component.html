<div class="flex flex-col gap-4 bg-white">

    <div class="flex justify-between gap-4 @max-md:flex-col">
        <span class="text-base font-medium">
            {{currentDateString}}
        </span>
        <bh-exchange-bar [exchangeRates]="exchangeRates()"></bh-exchange-bar>
    </div>

    <div class="flex gap-4 @max-md:flex-col">

        <div class="flex-1 flex flex-col rounded-lg bg-coolgray-100 px-3 py-4 min-h-34 justify-between">
            <span class="text-base font-medium"><PERSON><PERSON><PERSON><PERSON><PERSON></span>
            <div class="flex flex-row gap-2">
                <div class="flex flex-1 flex-col gap-2">
                    <h2>{{sales() | number:'1.2-2'}} TL</h2>
                    <div class="min-h-[18px] text-xs flex gap-1 items-center">
                        <span>
                            d<PERSON>ne kıyas<PERSON>
                        </span>
                        <div class="p-0.5 rounded-full font-medium" [ngClass]="{
                            'bg-lime-300': salesChangeType() === 'increase',
                            'bg-red-100': salesChangeType() === 'decrease'
                        }">

                            @if(salesChangeType() === 'increase') {
                            <span class="text-forest-600">+{{salesChange()}}%</span>
                            }
                            @else {
                            <span class="text-red-500">-{{salesChange()}}%</span>
                            }
                        </div>
                    </div>
                </div>
                <div class="flex flex-1 gap-2 items-center justify-center">
                    <bh-gradient-line [series]="salesSeries()" [width]="120" [height]="40" />
                </div>
            </div>
        </div>

        <div class="flex-1 flex flex-col rounded-lg bg-coolgray-100 px-3 py-4 min-h-34 justify-between">
            <span class="text-base font-medium">Bugünkü Tahsilat</span>
            <div class="flex flex-row gap-2">
                <div class="flex flex-1 flex-col gap-2">
                    <h2>{{income() | number:'1.2-2'}} TL</h2>
                    <div class="min-h-[18px] text-xs flex gap-1 items-center">
                        <span>
                            düne kıyasla
                        </span>
                        <div class="p-0.5 rounded-full font-medium" [ngClass]="{
                            'bg-lime-300': incomeChangeType() === 'increase',
                            'bg-red-100': incomeChangeType() === 'decrease'
                        }">

                            @if(incomeChangeType() === 'increase') {
                            <span class="text-forest-600">+{{incomeChange()}}%</span>
                            }
                            @else {
                            <span class="text-red-500">-{{incomeChange()}}%</span>
                            }
                        </div>
                    </div>
                </div>
                <div class="flex flex-1 gap-2 items-center justify-center">
                    <bh-gradient-line [series]="incomeSeries()" [width]="120" [height]="40" />
                </div>
            </div>

        </div>

    </div>
</div>