
import { DOCUMENT } from '@angular/common';
import { inject, Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { filter, take } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class SplashScreenService {
    private document = inject(DOCUMENT);
    private router = inject(Router);

    constructor() {
        this.router.events.pipe(
            filter(event => event instanceof NavigationEnd),
            take(1)
        ).subscribe(() => {
            this.hide();
        });
    }

    show() {
        this.document.body.classList.remove('bh-splash-screen-hidden');
    }

    hide() {
        this.document.body.classList.add('bh-splash-screen-hidden');
    }
}