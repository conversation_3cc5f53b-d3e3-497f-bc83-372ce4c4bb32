/* PrimeNG Components */
@layer components {

  /* Button */
  .p-button {
    border-radius: var(--radius-sm);
    gap: calc(var(--spacing) * 3);
    padding: calc((var(--spacing) * 2) - 1px) calc((var(--spacing) * 4) - 1px);
    font-size: var(--text-base);
    font-weight: var(--font-weight-medium);
    line-height: var(--text-base--line-height);

    &:disabled {
      background: var(--color-coolgray-200);
      color: var(--color-coolgray-400);
      border-color: var(--color-coolgray-200);
    }
  }

  .p-button-primary {
    background: var(--color-coolgray-1000);
    color: var(--color-white);
    border-color: var(--color-coolgray-1000);

    &:not(:disabled):hover,
    &:not(:disabled):active {
      border-color: var(--color-coolgray-900);
      background: var(--color-coolgray-900);
    }

    &:focus-visible {
      outline-color: var(--color-coolgray-800);
    }
  }

  .p-button-secondary {
    background: var(--color-blue-500);
    color: var(--color-white);
    border-color: var(--color-blue-500);

    &:not(:disabled):hover,
    &:not(:disabled):active {
      border-color: var(--color-blue-700);
      background: var(--color-blue-700);
    }

    &:focus-visible {
      outline-color: var(--color-blue-500);
    }
  }

  .p-button-plain {
    color: var(--color-coolgray-900);
    border-color: transparent;
    background: transparent;

    &:not(:disabled):hover,
    &:not(:disabled):active {
      background: var(--color-coolgray-200);
      color: var(--color-coolgray-1000);
    }

    &:focus-visible {
      outline-color: var(--color-coolgray-800);
    }

    &:disabled {
      border-color: transparent;
      background: transparent;
    }
  }

  .p-button-text {
    color: var(--color-coolgray-900);
    border-color: transparent;
    background: transparent;

    &:not(:disabled):hover,
    &:not(:disabled):active {
      background: var(--color-coolgray-200);
      color: var(--color-coolgray-1000);
    }

    &:focus-visible {
      outline-color: var(--color-coolgray-800);
    }

    &:disabled {
      border-color: transparent;
      background: transparent;
    }
  }

  .p-button-outlined {
    color: var(--color-coolgray-900);
    border-color: var(--color-coolgray-800);
    background: transparent;

    &:not(:disabled):hover,
    &:not(:disabled):active {
      background: var(--color-coolgray-200);
      color: var(--color-coolgray-1000);
    }

    &:focus-visible {
      outline-color: var(--color-coolgray-800);
    }

    &:disabled {
      border-color: var(--color-coolgray-300);
    }
  }

  .p-button-lg {
    padding: calc((var(--spacing) * 3) - 1px) calc((var(--spacing) * 6 - 1px));
  }

  .p-button-sm {
    padding: calc((var(--spacing) * 1.5) - 1px) calc((var(--spacing) * 4 - 1px));
    font-size: var(--text-sm);
    line-height: var(--text-sm--line-height);
  }

  .p-button-xs {
    padding: calc((var(--spacing) * 1) - 1px) calc((var(--spacing) * 2 - 1px));
    font-size: var(--text-xs);
    line-height: var(--text-xs--line-height);
    gap: calc(var(--spacing) * 2);
  }

  .p-button-icon-only {
    padding: unset;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: calc(var(--spacing) * 9.5);
    height: calc(var(--spacing) * 9.5);
  }

  .p-button-icon-only.p-button-lg {
    width: calc(var(--spacing) * 11.5);
    height: calc(var(--spacing) * 11.5);
  }

  .p-button-icon-only.p-button-sm {
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
  }

  .p-button-icon-only.p-button-xs {
    width: calc(var(--spacing) * 6);
    height: calc(var(--spacing) * 6);
  }

  .p-button-rounded {
    border-radius: var(--p-button-rounded-border-radius) !important;
  }

  .p-button-loading {
    color: var(--color-coolgray-700) !important;
  }


  /* Menu */
  .p-menu-overlay {
    box-shadow: 0px var(--spacing) calc(var(--spacing) * 5) calc(var(--spacing) / 4) var(--color-coolgray-200);
    margin-top: var(--spacing);
    border-radius: var(--radius-sm);
    border: calc(var(--spacing) / 4) solid var(--color-coolgray-300);
  }


  /* Alert Dialog Styles */
  .alert-dialog {
    .p-dialog-content {
      padding: 0;
      overflow-y: visible;
    }

    &.p-dialog {
      box-shadow: none;

      .p-dialog-content {
        border-radius: 0 0 calc(var(--spacing) * 2) calc(var(--spacing) * 2);
      }
    }
  }


  // Form Dialog Styles
  .form-dialog {

    .p-dialog-header {
      padding: calc(var(--spacing) * 4) calc(var(--spacing) * 4);
      border-bottom: 1px solid var(--color-coolgray-200);

      .p-dialog-title {
        font-size: 1.125rem;
        line-height: 1.5rem;
        font-weight: 500;
      }

      .p-dialog-close-button {
        width: calc(var(--spacing) * 5);
        height: calc(var(--spacing) * 5);

        &:focus {
          background-color: transparent;
        }

        &:hover {
          background-color: transparent;
        }
      }
    }

    &.p-dialog {
      box-shadow: none;
    }

    .p-dialog-content {
      padding: calc(var(--spacing) * 4) calc(var(--spacing) * 4) calc(var(--spacing) * 6) calc(var(--spacing) * 4);
    }
  }

  // Toast Styles
  .p-toast {
    .p-toast-message {
      border-radius: calc(var(--spacing) * 2);
      box-shadow: 0px var(--spacing) calc(var(--spacing) * 5) var(--color-coolgray-300);
      border: none;
      background-color: var(--color-coolgray-1000);
      color: var(--color-white);
      padding: calc(var(--spacing) * 4);

      .p-toast-message-content {
        display: flex;
        align-items: center;
        padding: 0;

        .p-toast-close-button {
          width: calc(var(--spacing) * 5);
          height: calc(var(--spacing) * 5);

          &:hover {
            background-color: transparent;
          }

          &:focus {
            background-color: transparent;
          }

          .p-toast-close-icon {
            width: calc(var(--spacing) * 3);
            height: calc(var(--spacing) * 3);
          }
        }
      }

      button {
        color: var(--color-white);
      }
    }

    .p-toast-message-success {
      background-color: var(--color-success-700);
    }

    //TODO: find the problem why it is not working with var
    .p-toast-message-error {
      background-color: #B91C2A;
    }

    .p-toast-message-warn {
      background-color: #DE981F;
    }

    .p-toast-message-info {
      background-color: #0064B1;
    }
  }
}