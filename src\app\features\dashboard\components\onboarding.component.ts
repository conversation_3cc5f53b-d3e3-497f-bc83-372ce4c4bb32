import { Component, input, model, OnInit, output, TemplateRef, ViewChild } from "@angular/core";
import { CommonModule } from '@angular/common';
import { IconComponent } from "../../../core/components/icon.component";
import { ProgressComponent } from "../../../core/components/progress/progress.component";
import { ButtonModule } from "primeng/button";


interface OnboardingStep {
    id: StepNames;
    title: string;
    completedTitle?: string;
    completed: boolean;
    templateRef?: TemplateRef<any>;
    subSteps?: OnboardingStep[];
    timeToComplete?: string;
}

enum StepNames {
    Product = 1,
    Customer = 2,
    Invoice = 3,
    Integrations = 4,
    BankAccount = 5,
    POS = 6,
    Opet = 7,
}

@Component({
    selector: 'bh-onboarding',
    templateUrl: './onboarding.component.html',
    imports: [CommonModule, IconComponent, ProgressComponent, ButtonModule],
    standalone: true,
})
export class OnboardingComponent implements OnInit {

    activeStepId = model<number>();
    showLater = output<boolean>();

    isInProgress = false;

    @ViewChild('step1Template', { static: true }) step1Template!: TemplateRef<any>;
    @ViewChild('step2Template', { static: true }) step2Template!: TemplateRef<any>;
    @ViewChild('step3Template', { static: true }) step3Template!: TemplateRef<any>;
    @ViewChild('step4Template', { static: true }) step4Template!: TemplateRef<any>;

    steps: OnboardingStep[] = [];
    stepNames = StepNames;

    currentMainStepId = StepNames.Product;

    constructor() {

        this.steps = [
            {
                id: StepNames.Product,
                title: 'İlk ürününüzü ekleyin',
                completedTitle: 'İlk ürününüzü eklediniz',
                completed: false,
                timeToComplete: '2 dk',
            },
            {
                id: StepNames.Customer,
                title: 'İlk müşterinizi ekleyin',
                completedTitle: 'İlk müşterinizi eklediniz',
                completed: false,
                timeToComplete: '2 dk',
            },
            {
                id: StepNames.Invoice,
                title: 'İlk faturanızı oluşturun',
                completedTitle: 'İlk faturanızı oluşturdunuz',
                completed: false,
                timeToComplete: '1 dk',
            },
            {
                id: StepNames.Integrations,
                title: 'Entegrasyonlarınızı tamamlayın',
                completedTitle: 'Entegrasyonlarınızı tamamladınız',
                completed: false,
                timeToComplete: '4 dk',
                subSteps: [
                    {
                        id: StepNames.BankAccount,
                        title: 'Banka hesaplarınızı bağlayın',
                        completed: false,
                    },
                    {
                        id: StepNames.POS,
                        title: 'Sanal POS’unuzu ekleyin',
                        completed: false,
                    },
                    {
                        id: StepNames.Opet,
                        title: 'Aracınızı Opet’e kaydedin',
                        completed: false,
                    },
                ]
            }
        ];

    }
    ngOnInit(): void {

        if (!this.activeStepId())
            this.activeStepId.set(1);

        this.steps.forEach(s => {
            if (s.templateRef === undefined) {
                const templateName = `step${s.id}Template`;
                if ((this as any)[templateName]) {
                    s.templateRef = (this as any)[templateName] as TemplateRef<any>;
                }
            }

            if (s.id < this.activeStepId()!) {
                if (s.subSteps) {
                    this.currentMainStepId = s.id;

                    s.subSteps.forEach(ss => {
                        if (ss.id < this.activeStepId()!) {
                            ss.completed = true;
                            this.isInProgress = true;
                        }
                    });
                    if (s.subSteps.every(ss => ss.completed)) {
                        s.completed = true;
                        this.isInProgress = false;
                    }
                }
                else {
                    s.completed = true;
                    this.currentMainStepId = s.id + 1;
                }
            }
        });

        console.log(this.isInProgress);
    }

    get completedStepCount() {
        return this.steps.filter(s => s.completed).length;
    }

    get totalStepCount() {
        return this.steps.length;
    }

    get activeTemplate() {
        const step = this.steps.find(s => s.id == this.currentMainStepId || s.subSteps?.find(ss => ss.id == this.currentMainStepId));
        if (!step)
            return null;
        return step.templateRef!;
    }

    getStep(stepName: StepNames) {
        return this.steps.find(s => s.id == stepName);
    }

}
