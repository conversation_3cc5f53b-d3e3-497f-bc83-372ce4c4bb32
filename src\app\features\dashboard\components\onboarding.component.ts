import { Component, inject, input, OnInit, output, TemplateRef, ViewChild } from "@angular/core";
import { CommonModule } from '@angular/common';
import { IconComponent } from "../../../core/components/icon.component";
import { ProgressComponent } from "../../../core/components/progress/progress.component";
import { ButtonModule } from "primeng/button";
import { ButtonComponent } from "../../../core/components/button/button.component";
import { RippleModule } from 'primeng/ripple';
import { RouterModule } from "@angular/router";
import { AlertDialogService } from "../../../core/services/application/alert-dialog.service";


interface OnboardingStep {
    id: StepNames;
    title: string;
    completedTitle?: string;
    completed: boolean;
    templateRef?: TemplateRef<any>;
    subSteps?: OnboardingStep[];
    timeToComplete?: string;
    url?: string;
}

enum StepNames {
    Product = 1,
    Customer = 2,
    Invoice = 3,
    Integrations = 4,
    BankAccount = 5,
    POS = 6,
    Opet = 7,
}

@Component({
    selector: 'bh-onboarding',
    templateUrl: './onboarding.component.html',
    imports: [CommonModule, IconComponent, ProgressComponent, ButtonModule, ButtonComponent, RippleModule, RouterModule],
    standalone: true,
})
export class OnboardingComponent implements OnInit {

    alertDialogService = inject(AlertDialogService);

    completedStepIds = input<number[]>([]);

    onShowLaterClicked = output<void>();
    onSkipIntegrationClicked = output<void>();

    isInProgress = false;

    @ViewChild('step1Template', { static: true }) step1Template!: TemplateRef<any>;
    @ViewChild('step2Template', { static: true }) step2Template!: TemplateRef<any>;
    @ViewChild('step3Template', { static: true }) step3Template!: TemplateRef<any>;
    @ViewChild('step4Template', { static: true }) step4Template!: TemplateRef<any>;

    steps: OnboardingStep[] = [];
    stepNames = StepNames;

    currentMainStepId = StepNames.Product;

    constructor() {

        this.steps = [
            {
                id: StepNames.Product,
                title: 'İlk ürününüzü ekleyin',
                completedTitle: 'İlk ürününüzü eklediniz',
                completed: false,
                timeToComplete: '2 dk',
            },
            {
                id: StepNames.Customer,
                title: 'İlk müşterinizi ekleyin',
                completedTitle: 'İlk müşterinizi eklediniz',
                completed: false,
                timeToComplete: '2 dk',
            },
            {
                id: StepNames.Invoice,
                title: 'İlk faturanızı oluşturun',
                completedTitle: 'İlk faturanızı oluşturdunuz',
                completed: false,
                timeToComplete: '1 dk',
            },
            {
                id: StepNames.Integrations,
                title: 'Entegrasyonlarınızı tamamlayın',
                completedTitle: 'Entegrasyonlarınızı tamamladınız',
                completed: false,
                timeToComplete: '4 dk',
                subSteps: [
                    {
                        id: StepNames.BankAccount,
                        title: 'Banka hesaplarınızı bağlayın',
                        completed: false,
                        url: "/accounts"
                    },
                    {
                        id: StepNames.POS,
                        title: 'Sanal POS’unuzu ekleyin',
                        completed: false,
                        url: "/pos-settings"
                    },
                    {
                        id: StepNames.Opet,
                        title: 'Aracınızı Opet’e kaydedin',
                        completed: false,
                        url: "/advantages"
                    },
                ]
            }
        ];

    }

    ngOnInit(): void {

        const completedStepIds = this.completedStepIds();

        this.steps.forEach(s => {
            if (s.templateRef === undefined) {
                const templateName = `step${s.id}Template`;
                if ((this as any)[templateName]) {
                    s.templateRef = (this as any)[templateName] as TemplateRef<any>;
                }
            }

            if (completedStepIds.includes(s.id)) {
                s.completed = true;
            }

            if (s.subSteps) {
                s.subSteps.forEach(ss => {
                    if (completedStepIds.includes(ss.id)) {
                        ss.completed = true;
                        if (!this.isInProgress)
                            this.isInProgress = true;
                    }
                });

                if (s.subSteps.every(ss => ss.completed)) {
                    s.completed = true;
                    this.isInProgress = false;
                }
                // else
                //     s.completed = false;
            }
        });

        if (completedStepIds)
            this.currentMainStepId = this.steps.find(s => !s.completed)?.id || this.steps[this.steps.length - 1].id;
        else
            this.currentMainStepId = this.steps[0].id;
    }

    get completedStepCount() {
        return this.steps.filter(s => s.completed).length;
    }

    get totalStepCount() {
        return this.steps.length;
    }

    get activeTemplate() {
        const step = this.steps.find(s => s.id == this.currentMainStepId || s.subSteps?.find(ss => ss.id == this.currentMainStepId));
        if (!step)
            return null;
        return step.templateRef!;
    }

    getStep(stepName: StepNames) {
        return this.steps.find(s => s.id == stepName);
    }

    uploadProductClicked() { }

    async skipIntegrationStepClicked() {

        const result = await this.alertDialogService.confirm(
            "Entegrasyon ayarlarına giderek bu adımı daha sonra da tamamlayabilirsiniz.",
            "Entegrasyonlarınızı tamamlamadınız!",
            "Tamamlamadan Devam Et",
            "Tamamla",
            'default',
            true
        );

        if (!result) return;

        const integrationStep = this.steps.find(s => s.id == StepNames.Integrations)!;
        integrationStep.completed = true;
        integrationStep.subSteps?.forEach(s => s.completed = true);

        this.isInProgress = false;

        const isAllCompleted = this.steps.every(s => s.completed);
        if (isAllCompleted) 
            this.alertDialogService.success("Başlangıç adımlarını tamamladınız. Uygulamayı kullanmaya devam edebilirsiniz.", "Artık hazırsınız!", "Devam Et");

        this.onSkipIntegrationClicked.emit();
    }
}
