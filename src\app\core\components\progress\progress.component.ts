
import { CommonModule } from '@angular/common';
import { Component, input, OnInit } from '@angular/core';

@Component({
    selector: 'bh-progress',
    templateUrl: './progress.component.html',
    imports: [CommonModule],
    standalone: true,
})

export class ProgressComponent implements OnInit {

    readonly value = input<number>(0);
    readonly max = input<number>(100);
    readonly isIndeterminate = input<boolean>(false);

    constructor() {
    }

    get progress() {
        if (this.value() > this.max())
            return 100;

        if (this.value() < 0)
            return 0;

        if (this.max() === 0)
            return 0;

        const value = this.value();

        if (value === 0)
            return 4;
        else
            return (this.value() / this.max()) * 100;
    }

    ngOnInit() { }
}