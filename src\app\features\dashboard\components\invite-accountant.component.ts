
import { Component, input, output } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { IconComponent } from "../../../core/components/icon.component";
import { CommonModule } from '@angular/common';

@Component({
    selector: 'bh-invite-accountant',
    template: `
        <div class="relative flex rounded-lg p-4 gap-4 bg-[url('/assets/images/accountant_invite_bg.svg')] bg-cover bg-center items-center @max-md:flex-col @max-md:items-start">
                <span class="text-coolgray-800">
                    Mali müşavirinizi Bizim He<PERSON>p’a davet edin. Ay sonunda tüm evraklarınızı tek tıkla buradan gönderin.
                </span>
                <p-button  variant="outlined" label="Müşavirini Davet Et" styleClass="min-w-50" [ngClass]="{ 'mr-8': isEditMode()}" >
                </p-button>
                @if(isEditMode()) {
                <p-button variant="text" (click)="hideClicked.emit()" class="absolute top-2 right-2" styleClass="p-button-xs">
                    <bh-icon name="close" [size]="16"></bh-icon>
                </p-button>
                }
        </div>
    `,
    imports: [ButtonModule, IconComponent, CommonModule],
    standalone: true,
})
export class InviteAccountantComponent {

    isEditMode = input<boolean>(false);

    hideClicked = output<void>();

}