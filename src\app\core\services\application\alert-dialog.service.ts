import { Injectable, inject } from '@angular/core';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { AlertDialogComponent } from '../../components/alert-dialog/alert-dialog.component';
import { firstValueFrom } from 'rxjs';
import { AlertDialogDataModel } from '../../models/alert-dialog-data.model';

@Injectable({
    providedIn: 'root'
})
export class AlertDialogService {
    private dialogService = inject(DialogService);

    /**
     * Shows a custom alert dialog
     * @param data The dialog configuration
     * @returns Promise that resolves to 'true' or 'false'
     */
    show(data: AlertDialogDataModel): Promise<boolean> {
        const ref: DynamicDialogRef = this.dialogService.open(AlertDialogComponent, {
            header: data.title,
            data: data,
            width: data.size == 'default' ? '500px' : data.size == 'small' ? '300px' : '800px',
            contentStyle: { 'border-radius': '8px', overflow: 'hidden' },
            baseZIndex: 10000,
            closeOnEscape: false,
            dismissableMask: false,
            modal: true,
            closable: false,
            styleClass: 'alert-dialog',
            draggable: false,
            resizable: false,
            showHeader: false,
        });

        return firstValueFrom(ref.onClose);
    }

    /**
     * Shows a success alert
     * @param message The message to display
     * @param title The dialog title (default: 'Başarılı')
     * @param okLabel The OK button label (default: 'Tamam')
     * @param size The dialog size (default: 'default')
     * @returns Promise that resolves when dialog is closed
     */
    success(message: string, title = 'Başarılı', okLabel = 'Tamam', size: 'default' | 'large' = 'default'): Promise<boolean> {
        return this.show({
            title,
            message,
            state: 'success',
            okLabel: okLabel,
            showCancel: false,
            size: size
        });
    }

    /**
     * Shows an error alert
     * @param message The message to display
     * @param title The dialog title (default: 'Hata')
     * @param okLabel The OK button label (default: 'Tamam')
     * @param size The dialog size (default: 'default')
     * @returns Promise that resolves when dialog is closed
     */
    error(message: string, title = 'Hata', okLabel = 'Tamam', size: 'default' | 'large' = 'default'): Promise<boolean> {
        return this.show({
            title,
            message,
            state: 'error',
            okLabel: okLabel,
            showCancel: false,
            size: size
        });
    }

    /**
     * Shows a warning alert
     * @param message The message to display
     * @param title The dialog title (default: 'Uyarı')
     * @param okLabel The OK button label (default: 'Tamam')
     * @param size The dialog size (default: 'default')
     * @returns Promise that resolves when dialog is closed
     */
    warning(message: string, title = 'Uyarı', okLabel = 'Tamam', size: 'default' | 'large' = 'default'): Promise<boolean> {
        return this.show({
            title,
            message,
            state: 'warning',
            okLabel: okLabel,
            showCancel: false,
            size: size
        });
    }

    /**
     * Shows an info alert
     * @param message The message to display
     * @param title The dialog title (default: 'Bilgi')
     * @param okLabel The OK button label (default: 'Tamam')
     * @param size The dialog size (default: 'default')
     * @returns Promise that resolves when dialog is closed
     */
    info(message: string, title = 'Bilgi', okLabel = 'Tamam', size: 'default' | 'large' = 'default'): Promise<boolean> {
        return this.show({
            title,
            message,
            state: 'info',
            okLabel: okLabel,
            showCancel: false,
            size: size
        });
    }

    /**
     * Shows a confirmation dialog
     * @param message The message to display
     * @param title The dialog title (default: 'Onay')
     * @param okLabel The OK button label (default: 'Evet')
     * @param cancelLabel The Cancel button label (default: 'Hayır')
     * @param size The dialog size (default: 'default')
     * @param isRevertable Whether the confirmation is revertable (default: false)
     * @param revertTimeout The revert timeout in milliseconds (default: 2000)
     * @returns Promise that resolves to 'true' or 'false'
     */
    confirm(message: string, title = 'Onay', okLabel = 'Evet', cancelLabel = 'Hayır', size: 'small' | 'default' | 'large' = 'default', isRevertable = false, revertTimeout = 2000): Promise<boolean> {
        return this.show({
            title,
            message,
            state: 'warning',
            okLabel,
            cancelLabel,
            showCancel: true,
            size: size,
            showAsConfirm: true,
            isRevertable: isRevertable,
            revertTimeout: revertTimeout
        });
    }
}