<div class="flex flex-col gap-4 bg-white">
    <div class="flex justify-between cursor-pointer">
        <span class="text-base font-medium"><PERSON><PERSON><PERSON><PERSON></span>
        <bh-icon name="chevron-right" [size]="16"></bh-icon>
    </div>

    <div class="flex flex-col gap-3">
        <div class="flex gap-1 items-center">
            <h4 class="ml-auto">{{totalValue | number:'1.2-2'}}</h4><span>{{currency}}</span>
        </div>

        <div class="flex flex-row gap-2 pb-1">
            @for (color of colorArray; track $index) {
            <div class="flex-1 h-4 rounded-xs" [style.background-color]="color">
            </div>
            }
        </div>

        @if(upcomingItems().length === 0) {
        <div class="flex flex-col gap-3 p-8 items-center text-center">
            <span class="text-coolgray-800 text-sm"><PERSON><PERSON><PERSON><PERSON> ödemelerinizi görmek için
                tedarikçilerinizi ekleyin.</span>
            <p-button variant="text" label="Tedarikçi Ekle" size="small">
            </p-button>
        </div>
        }
        @else {
        @for (item of upcomingItems(); track $index) {
        <div class="flex gap-2 items-center">
            <div class="w-2 h-2 rounded-xs" [style.background-color]="item.colorHex">
            </div>
            <span class="text-coolgray-800 text-sm">{{item.label}}</span>
            <div class="flex gap-2 ml-auto items-baseline">
                <span class="ml-auto text-coolgray-700 text-sm">{{item.value | number:'1.2-2'}}
                    {{item.currency}}</span>
                @if (item.link) {
                <a [routerLink]="item.link">
                    <bh-icon name="chevron-right" [size]="16"></bh-icon>
                </a>
                }
            </div>

        </div>
        }
        }
    </div>
</div>