import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToastService } from '../../services/application/toast.service';
import { ToastModule } from 'primeng/toast';
import { IconComponent } from '../icon.component';
import { ButtonModule } from 'primeng/button';

@Component({
    selector: 'bh-toast',
    templateUrl: './toast.component.html',
    imports: [CommonModule, ToastModule, IconComponent, ButtonModule],
    standalone: true,
})
export class ToastComponent {

    toastService: ToastService = inject(ToastService);

    onToastAction() {
        this.toastService.emitAction();
    }
}