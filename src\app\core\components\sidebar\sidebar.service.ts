import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';

@Injectable({ providedIn: 'root' })
export class SidebarService {
    router: Router = inject(Router);
    loadedUrl = this.router.url;

    collapsed = false;

    menuItems: MenuItem[] = this.getMenu();

    toggleSidebar() {
        this.collapsed = !this.collapsed;
    }

    toggleMenuItemExpand(item: MenuItem) {
        var expanded = !item.expanded;

        if (expanded) {
            this.menuItems.forEach(i => i.expanded = false);
        }

        item.expanded = expanded;
    }

    private getMenu(): MenuItem[] {
        var currentUrl = this.loadedUrl;
        var items: MenuItem[] = [
            { label: 'Güncel Durum', icon: 'line-chart-up-01', link: '/dashboard' },
            { label: 'Müşteriler', icon: 'users-01', link: '/customers' },
            { label: 'Tedarikçiler', icon: 'coins-04', link: '/suppliers' },
            { label: 'Satışlar', icon: 'receipt', link: '/sales' },
            {
                label: 'Alışlar', icon: 'coins-hand', children: [
                    { label: 'Alış Faturaları', link: '/purchases' },
                    { label: 'Gelen E-Faturalar', link: '/incoming-edocuments' }
                ]
            },
            { label: 'Teklifler', icon: 'receipt', link: '/proposals' },
            {
                label: 'Ürünler', icon: 'tag-01', children: [
                    { label: 'Ürün / Hizmet Tanımları', link: '/products' },
                    { label: 'Depolar', link: '/warehouses' },
                    { label: 'Özel Fiyat Listeleri', link: '/price-lists' },
                    { label: 'Üretim', link: '/production' },
                    { label: 'Kataloglarınız', link: '/catalogs' },
                    { label: 'Ürün Varyantları', link: '/variants' },
                ]
            },
            {
                label: 'Nakit Yönetimi', icon: 'bank-note-02', children: [
                    { label: 'Hesaplarım', link: '/accounts' },
                    { label: 'Masraflar', link: '/costs' },
                    { label: 'Çek Portföyü', link: '/cheque-portfolio' },
                    { label: 'Senet Portföyü', link: '/bond-portfolio' },
                    { label: 'Krediler', link: '/credits' },
                    { label: 'Çalışanlar', link: '/employees' },
                    { label: 'Projeler', link: '/projects' },
                    { label: 'Demirbaşlar', link: '/assets' },
                ]
            },
            {
                label: 'E-Ticaret', icon: 'building-02', children: [
                    { label: 'Satışlar', link: '/bulk-sales' },
                    { label: 'Entegrasyon Ayarları', link: '/e-commerce-settings' },
                    { label: 'Ürün Eşleştirme', link: '/product-match' },
                    { label: 'Listeleme', link: '/product-list' },
                    { label: 'Fiyat Güncelleme', link: '/marketplace-prices' },
                    { label: 'Müşteri Soruları', link: '/marketplace-messages' },
                    { label: 'İstatistikler', link: '/e-commerce-portal' },
                    { label: 'Kargo Entegrasyonu', link: '/cargo-settings' },
                    { label: 'Mutabakat', link: '/e-commerce-settlement' },
                ]
            },
            { label: 'Bizim Sipariş', icon: 'shopping-bag-03', link: '/bizim-siparis' },
            { label: 'Bizim Muhasebeci', icon: 'calculator', link: '/bizim-muhasebeci' },
            {
                label: 'Raporlar', icon: 'file-07', children: [
                    { label: 'Satışlar - Alışlar', link: '/document-reports' },
                    { label: 'Finansal Raporlar', link: '/financial-reports' },
                    { label: 'Stok Raporları', link: '/stock-reports' },
                    { label: 'Müşteri Listesi', link: '/store-list-report' }
                ]
            },
            { label: 'Avantajlar', icon: 'star-01', link: '/advantages' },
            {
                label: 'Ayarlar', icon: 'settings-02', children: [
                    { label: 'Kullanıcılar', link: '/users' },
                    { label: 'Tanımlar', link: '/master-data' },
                    { label: 'E-Fatura Başvuru', link: '/e-invoice-application' },
                    { label: 'E-Fatura Ayarları', link: '/e-invoice-settings' },
                    { label: 'E-Fatura POS Ayarları', link: '/pos-settings' },
                    { label: 'Teklif ve Özel Şablonlar', link: '/proposal-templates' },
                    { label: 'Etiket Şablonları', link: '/label-templates' },
                    { label: 'SMS Ayarları', link: '/sms-settings' }
                ]
            },
            { label: 'Destek', icon: 'headphones-01', link: '/support' },
        ];

        if (currentUrl.length > 0 && currentUrl !== '/') {
            items.forEach(item => {
                if (item.children) {
                    item.children.forEach(child => {
                        if (child.link === currentUrl)
                            item.expanded = true;
                    })
                }
            });
        }

        console.log('items', items);

        return items;
    }

    menuItemClicked(item: MenuItem, isChild: boolean) {
        console.log('Menu item clicked:', item, isChild);

        if (!isChild)
            this.menuItems.forEach(i => i.expanded = false);
    }
}

export interface MenuItem {
    label?: string;
    icon?: string;
    link?: string;
    children?: MenuItem[];
    badge?: string;
    active?: boolean;
    expanded?: boolean;
}