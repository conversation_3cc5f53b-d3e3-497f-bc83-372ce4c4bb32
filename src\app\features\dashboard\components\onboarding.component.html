<div class="w-full rounded-lg p-4 bg-white">
    <div class="flex flex-col gap-4 text-base">
        <div class="flex flex-wrap">
            <div class="flex flex-col gap-4">
                <h4>
                    Aramıza hoş geldiniz!
                </h4>
                <span class="@max-md:hidden text-coolgray-800">
                    İşletmenizin hesabını tutmak için muhasebeci olmanıza gerek yok.
                    <br>
                    <PERSON><PERSON> adımlarınızı kolayca tamamlayarak programa başlayın.
                </span>
                <span class="@max-md:block @md:hidden text-coolgray-800">
                    Bizim Hesap’ı tanımanızı sağlayacak ilk adımları tamamlamak için bilgisayarınızdan uygulamaya giriş
                    yapabilirisiniz.
                </span>
            </div>
            <div class="ml-auto content-end mt-6">
                <div class="flex gap-2 text-sm items-center">

                    <bh-progress [value]="completedStepCount" [max]="totalStepCount"
                        [isIndeterminate]="isInProgress"></bh-progress>

                    <span class="text-coolgray-700"
                        [class]="{ 'text-coolgray-900': completedStepCount === totalStepCount }">{{completedStepCount}}/{{totalStepCount}}
                        Tamamlandı</span>
                </div>
            </div>
        </div>

        <div class="flex gap-4 @max-md:hidden">

            <div class="flex-2/5">
                <ul class="flex flex-col gap-4 w-full">
                    @for (step of steps; track $index) {
                    <li pRipple class="bg-white border-coolgray-200 border px-3 py-4 rounded-lg cursor-pointer"
                        (click)="currentMainStepId = step.id"
                        [ngClass]="{ 'bg-blue-50! border-blue-500! border!': step.id === currentMainStepId}">

                        <div class="flex gap-2 items-center">

                            <span class="text-coolgray-500" [ngClass]="{ 'text-blue-500!': step.id === currentMainStepId,
                                    'text-success-500!' : step.completed
                                 }">
                                <bh-icon [name]="step.completed ? 'filled-check' : 'empty-check'"></bh-icon>
                            </span>
                            <span>{{ step.title }}</span>
                            <span class="ml-auto text-coolgray-700">{{ step.completed ? 'Tamamlandı' :
                                step.timeToComplete }}</span>

                        </div>
                    </li>
                    }
                </ul>
            </div>

            <div class="flex-3/5 rounded-lg py-2 px-5 bg-[url('/assets/images/onboarding_bg.png')] bg-cover">
                <ng-container [ngTemplateOutlet]="activeTemplate"></ng-container>
            </div>

        </div>

        <div class="flex gap-4 justify-end @max-md:hidden">
            <bh-button variant="plain" size="sm" (click)="onShowLaterClicked.emit()">Sonra Göster</bh-button>
        </div>
    </div>
</div>

<ng-template #step1Template>

    <div class="flex flex-row items-center h-full @container">
        <div class="flex flex-col gap-6">
            <span class="text-coolgray-800">
                Ürünlerinizi eklemek için kayıt açın veya ürünlerinizin yer aldığı Excel dosyasını yükleyin.
            </span>
            <div class="flex gap-2">
                <bh-button size="lg" [variant]="getStep(stepNames.Product)!.completed ? 'outlined' : 'primary'"
                    [routerLink]="'/products'">Ürün
                    Ekle</bh-button>
                <bh-button variant="plain" size="lg" (click)="uploadProductClicked()">Excel'den Yükle</bh-button>
            </div>
        </div>
        <img src="/assets/images/onboarding_step1.png" alt="onboarding_product" class="@max-md:hidden">
    </div>


</ng-template>

<ng-template #step2Template>
    <div class="flex flex-row items-center h-full @container">
        <div class="flex flex-col gap-6">
            <span class="text-coolgray-800">
                Sürekli iletişim kurduğunuz müşterinizi sisteme kaydederek kolayca işlem yapın.
            </span>
            <div class="flex gap-2">
                <bh-button size="lg" [variant]="getStep(stepNames.Customer)!.completed ? 'outlined' : 'primary'"
                    [routerLink]="'/customers'">Müşteri
                    Ekle</bh-button>
            </div>
        </div>
        <img src="/assets/images/onboarding_step2.png" alt="onboarding_customer" class="@max-md:hidden">
    </div>
</ng-template>

<ng-template #step3Template>
    <div class="flex flex-row items-center h-full @container">
        <div class="flex flex-col gap-6">
            <span class="text-coolgray-800">
                Birkaç adımda faturanızı oluşturun veya
                e-Fatura entegrasyonunu tamamlayarak fatura kesin.
            </span>
            <div class="flex gap-2">
                @if (!getStep(stepNames.Invoice)!.completed) {
                <bh-button variant="plain" size="lg" [routerLink]="'/e-invoice-settings'">e-Faturamı Bağla</bh-button>
                <bh-button size="lg" [routerLink]="'/sales'">Fatura Oluştur</bh-button>
                }
                @else {
                <bh-button size="lg" variant="outlined" [routerLink]="'/sales'">Fatura
                    Oluştur</bh-button>
                }
            </div>
        </div>
        <img src="/assets/images/onboarding_step3.png" alt="onboarding_invoice" class="@max-md:hidden">
    </div>
</ng-template>

<ng-template #step4Template>

    <div class="flex flex-row gap-2 items-center h-full @container">
        <div class="flex flex-col gap-4">
            <span class="text-coolgray-800">
                Avantajlı entegrasyonlarla tüm işlemlerinizi Bizim Hesap’tan yönetin.
            </span>
            <ul class="flex flex-col gap-4 w-full text-coolgray-800">
                @for (subStep of getStep(stepNames.Integrations)!.subSteps; track $index) {
                <li class="flex gap-2 items-center cursor-pointer" [routerLink]="subStep.url">
                    @if (subStep.completed) {
                    <span class="text-success-500">
                        <bh-icon name="filled-check"></bh-icon>
                    </span>
                    }
                    @else{
                    <span class="text-blue-500">
                        <bh-icon name="empty-check"></bh-icon>
                    </span>
                    }
                    <span>{{ subStep.title }}</span>
                    <span class="ml-auto text-coolgray-900"><bh-icon name="chevron-right" [size]="16"></bh-icon></span>
                </li>
                }
            </ul>
            @if (!getStep(stepNames.Integrations)!.completed) {
            <div class="flex gap-2">
                <bh-button size="sm" variant="plain" (click)="skipIntegrationStepClicked()">Bu Adımı Atla</bh-button>
            </div>
            }
        </div>
        <img src="/assets/images/onboarding_step4.png" alt="onboarding_integration" class="@max-md:hidden">
    </div>
</ng-template>