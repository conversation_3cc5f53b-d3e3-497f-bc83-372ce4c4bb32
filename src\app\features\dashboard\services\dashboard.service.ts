import { Injectable, signal } from '@angular/core';
import { DashboardComponentModel } from '../models/dashboard-component.model';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  private dashboardComponentsStorageKey = 'dashboardComponents';
  private showValuesStorageKey = 'showValues';

  private allWidgets: DashboardComponentModel[] = [
    { id: 'onboarding', title: 'Onboarding', componentType: 'onboarding', isStatic: true, order: 0, width: 'full', show: true, dropZone: 'full' },
    { id: 'daily-sales', title: 'Günlük Satış ve Gelir', componentType: 'daily-sales', isStatic: false, order: 1, width: 'wide', show: true, dropZone: 'wide' },
    { id: 'total-net-value', title: 'Toplam Net Değer', componentType: 'total-net-value', isStatic: false, order: 2, width: 'wide', show: true, dropZone: 'wide' },
    { id: 'invite-accountant', title: '<PERSON><PERSON><PERSON><PERSON><PERSON>t', componentType: 'invite-accountant', isStatic: true, order: 3, width: 'wide', show: true, dropZone: 'wide' },
    { id: 'upcoming-payments', title: 'Yaklaşan Ödemeler', componentType: 'upcoming-payments', isStatic: false, order: 4, width: 'narrow', show: true, dropZone: 'narrow' },
    { id: 'overdue-payments', title: 'Vadesi Geçen Ödemeler', componentType: 'overdue-payments', isStatic: false, order: 5, width: 'narrow', show: true, dropZone: 'narrow' },
    { id: 'incoming-invoices', title: 'Gelen E-Faturalar', componentType: 'incoming-invoices', isStatic: false, order: 6, width: 'narrow', show: true, dropZone: 'narrow' },
    { id: 'costs', title: 'Masraflar', componentType: 'costs', isStatic: false, order: 7, width: 'narrow', show: true, dropZone: 'narrow' },
    { id: 'cheques', title: 'Çekler', componentType: 'cheques', isStatic: false, order: 8, width: 'narrow', show: true, dropZone: 'narrow' },
    { id: 'bonds', title: 'Senetler', componentType: 'bonds', isStatic: false, order: 9, width: 'narrow', show: true, dropZone: 'narrow' },
    { id: 'credits', title: 'Krediler', componentType: 'credits', isStatic: false, order: 10, width: 'narrow', show: true, dropZone: 'narrow' },
    { id: 'giro', title: 'Ciro', componentType: 'giro', isStatic: false, order: 11, width: 'wide', show: true, dropZone: 'wide' },
  ];

  private dashboardComponentsSignal = signal<DashboardComponentModel[]>(this.allWidgets);
  dashboardComponents = this.dashboardComponentsSignal.asReadonly();

  private showValuesSignal = signal<boolean>(true);
  showValues = this.showValuesSignal.asReadonly();

  getAvailableWidgets(): any[] {
    const currentComponents = this.dashboardComponentsSignal();
    const availableWidgets = this.allWidgets.filter(widget =>
      !currentComponents.some(comp => comp.componentType === widget.componentType)
    );
    availableWidgets.forEach(widget => {
      widget.id = `${widget.id.split('_')[0]}_${Date.now()}`;
      widget.show = true;
    });
    return availableWidgets;
  }

  addComponent(component: DashboardComponentModel): void {
    const currentComponents = this.dashboardComponentsSignal();
    this.dashboardComponentsSignal.set([...currentComponents, component]);
  }

  removeComponent(componentId: string): void {
    const currentComponents = this.dashboardComponentsSignal();
    const filteredComponents = currentComponents.filter(c => c.id !== componentId || c.isStatic);
    this.dashboardComponentsSignal.set(filteredComponents);
  }

  removeHiddenComponents(): void {
    const currentComponents = this.dashboardComponentsSignal();
    const filteredComponents = currentComponents.filter(c => c.show === true);
    this.dashboardComponentsSignal.set(filteredComponents);
  }

  reorderComponents(componentIds: string[]): void {
    const currentComponents = this.dashboardComponentsSignal();
    const reorderedComponents = componentIds.map((id, index) => {
      const component = currentComponents.find(c => c.id === id);
      return { ...component!, order: index };
    });
    this.dashboardComponentsSignal.set(reorderedComponents);
  }

  saveUserPreferences(): void {
    localStorage.setItem(this.dashboardComponentsStorageKey, JSON.stringify(this.dashboardComponentsSignal()));
  }

  loadUserPreferences(): void {
    const savedComponents = localStorage.getItem(this.dashboardComponentsStorageKey);
    if (savedComponents) {
      this.dashboardComponentsSignal.set(JSON.parse(savedComponents));
    }

    const savedShowValues = localStorage.getItem(this.showValuesStorageKey);
    if (savedShowValues !== null) {
      this.showValuesSignal.set(JSON.parse(savedShowValues));
    }
  }

  toggleShowValues(): void {
    this.showValuesSignal.set(!this.showValuesSignal());
    localStorage.setItem(this.showValuesStorageKey, JSON.stringify(this.showValuesSignal()));
  }
}