import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { DashboardComponentModel } from '../models/dashboard-component.model';

@Injectable({
  providedIn: 'root'
})
export class DashboardService {

  private localStorageKey = 'dashboardComponents';

  private allWidgets: DashboardComponentModel[] = [
    { id: 'onboarding', title: 'Onboarding', componentType: 'onboarding', isStatic: true, order: 0, width: 'full', show: true },
    { id: 'daily-sales', title: '<PERSON><PERSON>nlük Satış ve Gelir', componentType: 'daily-sales', isStatic: false, order: 1, width: 'wide', show: true },
    { id: 'total-net-value', title: 'Toplam Net Değer', componentType: 'total-net-value', isStatic: false, order: 2, width: 'wide', show: true },
    { id: 'invite-accountant', title: '<PERSON><PERSON><PERSON><PERSON><PERSON>', componentType: 'invite-accountant', isStatic: true, order: 3, width: 'wide', show: true },
    { id: 'upcoming-payments', title: '<PERSON><PERSON><PERSON><PERSON>', componentType: 'upcoming-payments', isStatic: false, order: 4, width: 'narrow', show: true },
    { id: 'overdue-payments', title: 'Vadesi Geçen Ödemeler', componentType: 'overdue-payments', isStatic: false, order: 5, width: 'narrow', show: true },
    { id: 'incoming-invoices', title: 'Gelen E-Faturalar', componentType: 'incoming-invoices', isStatic: false, order: 6, width: 'narrow', show: true },
    { id: 'costs', title: 'Masraflar', componentType: 'costs', isStatic: false, order: 7, width: 'narrow', show: true },
    { id: 'cheques', title: 'Çekler', componentType: 'cheques', isStatic: false, order: 8, width: 'narrow', show: true },
    { id: 'bonds', title: 'Senetler', componentType: 'bonds', isStatic: false, order: 9, width: 'narrow', show: true },
    { id: 'credits', title: 'Krediler', componentType: 'credits', isStatic: false, order: 10, width: 'narrow', show: true },
    { id: 'giro', title: 'Giro', componentType: 'giro', isStatic: false, order: 11, width: 'wide', show: true },
  ];

  private dashboardComponentsSubject = new BehaviorSubject<DashboardComponentModel[]>(this.allWidgets);

  get dashboardComponents$(): Observable<DashboardComponentModel[]> {
    return this.dashboardComponentsSubject.asObservable();
  }

  getAvailableWidgets(): any[] {
    const currentComponents = this.dashboardComponentsSubject.value;
    const availableWidgets = this.allWidgets.filter(widget =>
      !currentComponents.some(comp => comp.componentType === widget.componentType)
    );
    availableWidgets.forEach(widget => {
      widget.id = `${widget.id}-${Date.now()}`;
      widget.show = true;
    });
    return availableWidgets;
  }

  addComponent(component: DashboardComponentModel): void {
    const currentComponents = this.dashboardComponentsSubject.value;
    this.dashboardComponentsSubject.next([...currentComponents, component]);
  }

  removeComponent(componentId: string): void {
    const currentComponents = this.dashboardComponentsSubject.value;
    const filteredComponents = currentComponents.filter(c => c.id !== componentId || c.isStatic);
    this.dashboardComponentsSubject.next(filteredComponents);
  }

  removeHiddenComponents(): void {
    const currentComponents = this.dashboardComponentsSubject.value;
    const filteredComponents = currentComponents.filter(c => c.show === true);
    this.dashboardComponentsSubject.next(filteredComponents);
  }

  reorderComponents(componentIds: string[]): void {
    const currentComponents = this.dashboardComponentsSubject.value;
    const reorderedComponents = componentIds.map((id, index) => {
      const component = currentComponents.find(c => c.id === id);
      return { ...component!, order: index };
    });
    this.dashboardComponentsSubject.next(reorderedComponents);
  }

  saveUserPreferences(): void {
    localStorage.setItem(this.localStorageKey, JSON.stringify(this.dashboardComponentsSubject.value));
  }

  loadUserPreferences(): void {
    const savedComponents = localStorage.getItem(this.localStorageKey);
    if (savedComponents) {
      this.dashboardComponentsSubject.next(JSON.parse(savedComponents));
    }
  }
}