@if (availableWidgets.length > 0) {
<p-button variant="outlined" size="large" (click)="showDrawer = true">
    <span>Mod<PERSON><PERSON>kle</span>
</p-button>
}

<p-drawer [visible]="showDrawer" position="bottom" styleClass="h-auto" (onHide)="showDrawer = false">
    <ng-template #header>
        <div class="flex flex-col gap-2">
            <span class="text-coolgray-900 text-lg">
                Mo<PERSON><PERSON>ller
            </span>

            <span class="text-coolgray-800">
                Sık kullandığınız modülleri sürükleyerek veya “ekle” butonuna basarak istediğiniz alana
                yerleştirebilirsiniz.
            </span>
        </div>
    </ng-template>
    <div class="flex gap-4 p-4 flex-wrap" cdkDropList [cdkDropListConnectedTo]="['dashboardList']">

        @for (widget of availableWidgets; track widget.id) {
        <div cdkDrag [cdkDragData]="widget" (cdkDragStarted)="showDrawer = false"
            class="rounded-lg bg-white flex outline outline-coolgray-300 gap-2 items-center p-4">
            <span cdkDragHandle class="cursor-move">{{widget.title}}</span>
            <p-button (click)="addWidget(widget)" size="small" variant="text" styleClass="p-button-xs">
                <span>Ekle</span>
                <bh-icon name="plus" [size]="16"></bh-icon>
            </p-button>
        </div>
        }
    </div>
</p-drawer>