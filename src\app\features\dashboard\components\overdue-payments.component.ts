
import { Component, input, OnInit } from '@angular/core';
import { IconComponent } from "../../../core/components/icon.component";
import { PaymentItemModel } from '../models/payment-item.model';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';

@Component({
    selector: 'bh-overdue-payments',
    templateUrl: 'overdue-payments.component.html',
    imports: [IconComponent, CommonModule, RouterModule, ButtonModule]
})
export class OverduePaymentsComponent implements OnInit {

    overdueItems = input<PaymentItemModel[]>([]);

    get totalValue() {
        return this.overdueItems().reduce((acc, item) => acc + item.value, 0);
    }

    get currency() {
        return this.overdueItems()[0]?.currency ?? '₺';
    }

    get colorArray() {

        let colorArray: [className: string, color: string][] = [];

        if (this.overdueItems().length === 0) {
            colorArray.push(['100%', '#EDF1F7']);
        }
        else {
            this.overdueItems().forEach(x => {
                const ratio = x.value / this.totalValue;
                const steps = Math.floor(ratio * 100);
                colorArray.push([`${steps}%`, x.colorHex]);
            });
        }
        return colorArray;
    }

    constructor() { }

    ngOnInit() { }


}