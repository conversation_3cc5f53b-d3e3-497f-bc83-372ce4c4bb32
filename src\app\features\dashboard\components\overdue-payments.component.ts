
import { Component, inject, model, OnInit } from '@angular/core';
import { IconComponent } from "../../../core/components/icon.component";
import { PaymentItemModel } from '../models/payment-item.model';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { BaseDashboardComponent } from './base-dashboard.component';
import { DashboardDataService } from '../services/dashboard-data.service';

@Component({
    selector: 'bh-overdue-payments',
    templateUrl: 'overdue-payments.component.html',
    imports: [IconComponent, CommonModule, RouterModule, ButtonModule],
    standalone: true,
    providers: [DashboardDataService],
})
export class OverduePaymentsComponent extends BaseDashboardComponent implements OnInit {

    dashboardDataService: DashboardDataService = inject(DashboardDataService);

    override loadData(): void {
        if (this.overdueItems().length > 0)
            return;

        this.loading.set(true);

        this.dashboardDataService.getOverduePayments().subscribe((data) => {
            this.overdueItems.set(data);
            this.loading.set(false);
        });
    }

    overdueItems = model<PaymentItemModel[]>([]);

    get totalValue() {
        return this.overdueItems().reduce((acc, item) => acc + item.value, 0);
    }

    get currency() {
        return this.overdueItems()[0]?.currency ?? '₺';
    }

    get colorArray() {

        let colorArray: [className: string, color: string][] = [];

        if (this.overdueItems().length === 0) {
            colorArray.push(['100%', '#EDF1F7']);
        }
        else {
            this.overdueItems().forEach(x => {
                const ratio = x.value / this.totalValue;
                const steps = Math.floor(ratio * 100);
                colorArray.push([`${steps}%`, x.colorHex]);
            });
        }
        return colorArray;
    }

    ngOnInit() {
        this.loadData();
    }
}