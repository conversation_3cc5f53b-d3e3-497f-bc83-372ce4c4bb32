<aside
  class="min-w-(--sidebar-width) max-w-(--sidebar-width) fixed top-0 left-0 bottom-0 bg-gray-1000 flex flex-col gap-y-6 text-white">

  <div class="px-8 pt-6">
    <div class="relative duration-400" [ngClass]="{'h-[46px]': !sidebarService.collapsed, 'h-[94px]': sidebarService.collapsed}">

      <img src="images/logo-sign.svg" alt="Logo" class="h-[46px] absolute left-0 top-0 duration-400" [ngClass]="{'opacity-0': !sidebarService.collapsed, 'opacity-100': sidebarService.collapsed}">
      <img src="images/logo-dark.svg" alt="Logo" class="h-[46px] absolute left-0 top-0 duration-400" [ngClass]="{'opacity-100': !sidebarService.collapsed, 'opacity-0': sidebarService.collapsed}">

      <div class="absolute right-0 flex px-[11px] duration-400" [ngClass]="{'bottom-[11px]': !sidebarService.collapsed, 'bottom-0': sidebarService.collapsed}">
        <button class="bg-white-100 hover:bg-white-50 rounded-full p-1 cursor-pointer duration-250"
          [ngClass]="{'-rotate-180': sidebarService.collapsed, 'rotate-0': !sidebarService.collapsed}" (click)="sidebarService.toggleSidebar()">
          <bh-icon name="chevron-left-double" [size]="16"></bh-icon>
        </button>
      </div>
    </div>
  </div>

  <bh-menu />

</aside>