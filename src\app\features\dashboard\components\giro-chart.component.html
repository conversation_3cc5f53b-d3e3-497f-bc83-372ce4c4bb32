<div class="flex flex-col gap-3">
    <div class="flex gap-1 items-center justify-between">
        <span class="text-base font-medium">Ciro</span>

        @if(loading()) {
        <div class="h-6 bg-linear-to-r from-coolgray-200 to-coolgray-50 rounded w-32"></div>
        }
        @else {
        <div class="flex gap-1 items-center">

            <div class="w-2 h-2 rounded-xs bg-forest-500">
            </div>
            <span class="text-sm">
                {{ currentMonthName }}
            </span>
            <span class="text-base font-medium text-coolgray-900 px-2">
                {{ showValues() ? (currentMonthValue | number:'1.2-2') : '*******'}} {{ currency() }}
            </span>

        </div>
        }

        <div class="flex gap-1 items-center cursor-pointer" [routerLink]="['/sales']">
            <span class="text-sm font-medium">Satış Raporuna Git</span>
            <bh-icon name="chevron-right" [size]="16"></bh-icon>
        </div>

    </div>
    @if (loading()) {
    <div class="h-[184px] bg-linear-to-r from-coolgray-200 to-coolgray-50 rounded-lg"></div>
    }
    @else {
    <p-chart #giroChart type="bar" [data]="chartData" [options]="chartOptions" height="184"></p-chart>
    }
</div>