<header class="h-(--toolbar-height) bg-white flex items-center px-8">
    <div class="w-full flex items-center justify-between">
        <div>
            <button pButton pRipple class="p-button p-component p-button-plain p-button p-[7px] gap-2"
                (click)="firmMenu.toggle($event)">
                <span>Parabasan</span>
                <bh-icon name="chevron-down" [size]="16" />
            </button>

            <p-menu #firmMenu [model]="firmMenuItems" title="Hızlı İşlem" [popup]="true" appendTo="body">
                <ng-template #item let-item>
                    <div
                        class="p-2 flex gap-1 items-center text-sm leading-5 text-coolgray-800 hover:text-coolgray-1000 cursor-pointer">
                        <span>{{item.label}}</span>
                    </div>
                </ng-template>
            </p-menu>
        </div>

        <div class="flex items-center gap-4">
            <bh-button size="sm"><PERSON><PERSON></bh-button>

            <div class="bg-coolgray-200 min-w-[1px] min-h-9"></div>

            <div class="flex gap-3">
                <bh-icon-button name="calendar" variant="plain" size="sm" class="size-[38px]"></bh-icon-button>
                <bh-icon-button name="announcement-01" variant="plain" size="sm" class="size-[38px]"></bh-icon-button>
                <bh-icon-button name="bell-01" variant="plain" size="sm" class="size-[38px]"></bh-icon-button>
            </div>

            <div class="flex items-center">
                <button pButton pRipple class="p-button p-component p-button-plain p-button-sm px-[7px] py-[4px] gap-2"
                    (click)="userMenu.toggle($event)">
                    <img src="https://primefaces.org/cdn/primeng/images/demo/avatar/asiyajavayant.png" alt="Avatar"
                        class="size-7 rounded-full" />
                    <bh-icon name="chevron-down" [size]="16" />
                </button>

                <p-menu #userMenu [model]="userMenuItems" title="Hızlı İşlem" [popup]="true" appendTo="body">
                    <ng-template #item let-item>
                        <div
                            class="p-2 flex gap-1 items-center text-sm leading-5 text-coolgray-800 hover:text-coolgray-1000 cursor-pointer">
                            <span>{{item.label}}</span>
                        </div>
                    </ng-template>
                </p-menu>
            </div>
        </div>
    </div>
</header>