import { Component, input } from "@angular/core";
import { ButtonComponent } from "../../../core/components/button/button.component";
import { RouterModule } from "@angular/router";

@Component({
    selector: 'bh-remaining-einvoice-counter',
    template: `
        @if(counter() <= 50) {
        <div class="flex py-0.5 gap-1 pl-5 pr-0.5 items-center rounded-full border border-coolgray-300 @max-md:hidden">
            <div class="flex gap-1.5">
                <span class="text-base font-normal"><PERSON><PERSON> kontör:</span>
                <span class="text-base font-medium">son {{counter()}}</span>
            </div>
            <bh-button [variant]="'plain'" [rounded]="true" size="sm" label="Kontör Al" rightIcon="chevron-right">
                Kontör Al
            </bh-button>
        </div>
        }
    `,
    standalone: true,
    imports: [ButtonComponent, RouterModule]
})

export class RemainingEInvoiceCounterComponent {

    readonly counter = input<number>(0);

    constructor() {
    }
}
