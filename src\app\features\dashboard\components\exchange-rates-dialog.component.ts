
import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { DynamicDialogConfig } from 'primeng/dynamicdialog';
import { ExchangeRateModel } from '../models/exchange-rate.model';
import { IconComponent } from '../../../core/components/icon.component';

@Component({
    selector: 'bh-exchange-rates-dialog',
    template: `
    <div class=" flex flex-col gap-6">

        <div class="flex flex-col gap-3 w-100">
            <div class="flex flex-row gap-2 text-coolgray-700 text-xs">
                <span class="flex-1">Döviz</span>
                <span class="flex-1"><PERSON><PERSON><PERSON></span>
                <span class="flex-1">Satış</span>
            </div>
            @for (rate of exchangeRates; track rate) {
            <div class="flex flex-row gap-2 items-center">
                <div class="flex-1 flex gap-1 items-center">
                    <bh-icon [name]="rate.icon" class="mr-1" [size]="16"></bh-icon>
                    <span class="text-sm">{{rate.currency}}</span>
                    <span class="text-xs font-medium" [ngClass]="{
                        'text-forest-600': rate.rateChange >= 0,
                        'text-red-600': rate.rateChange < 0
                    }">{{rate.rateChange >= 0 ? '+' : '-'}}%{{ getAbsValue(rate.rateChange)}}</span>
                </div>
                <div class="flex-1 flex gap-1 items-center text-coolgray-700 text-sm">
                    <span class="font-medium">{{rate.buyingRate | number:'1.4-4'}}</span>
                    <span>TRY</span>
                </div>
                <div class="flex-1 flex gap-1 items-center text-coolgray-700 text-sm">
                    <span class="font-medium">{{rate.sellingRate | number:'1.4-4'}}</span>
                    <span>TRY</span>
                </div>
            </div>
            }
        </div>

        <span class="text-xs text-coolgray-700">
            Merkez Bankası kur verileri esas alınmıştır.
        </span>
    </div>

    `,
    imports: [CommonModule, IconComponent],
    standalone: true,
})

export class ExchangeRatesDialogComponent implements OnInit {

    config = inject(DynamicDialogConfig);

    exchangeRates?: ExchangeRateModel[];

    constructor() {
    }

    ngOnInit() {

        this.exchangeRates = this.config.data as ExchangeRateModel[];
    }

    getAbsValue(value?: number) {
        if (!value) return 0;
        return Math.abs(value);
    }
}