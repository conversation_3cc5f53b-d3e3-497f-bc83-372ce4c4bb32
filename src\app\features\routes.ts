import { Route } from '@angular/router';
import { LayoutComponent } from '../layouts/layout.component';
import { initialDataResolver } from '../app.resolvers';

export const ROUTES: Route[] = [
    { path: '', redirectTo: 'dashboard', pathMatch: 'full' },
    {
        path: '',
        component: LayoutComponent,
        resolve: { initialData: initialDataResolver },
        children: [
            { path: 'dashboard', loadChildren: () => import('./dashboard/routes').then(m => m.ROUTES) },
            { path: 'customers', loadChildren: () => import('./customers/routes').then(m => m.ROUTES) },
            { path: 'suppliers', loadChildren: () => import('./suppliers/routes').then(m => m.ROUTES) },
            { path: 'sales', loadChildren: () => import('./sales/routes').then(m => m.ROUTES) },
            { path: 'purchases', loadChildren: () => import('./purchases/routes').then(m => m.ROUTES) },
            { path: 'incoming-edocuments', loadChildren: () => import('./incoming-edocuments/routes').then(m => m.ROUTES) },
            { path: 'proposals', loadChildren: () => import('./proposals/routes').then(m => m.ROUTES) },
            { path: 'products', loadChildren: () => import('./products/routes').then(m => m.ROUTES) },
            { path: 'warehouses', loadChildren: () => import('./warehouses/routes').then(m => m.ROUTES) },
            { path: 'price-lists', loadChildren: () => import('./price-lists/routes').then(m => m.ROUTES) },
            { path: 'production', loadChildren: () => import('./production/routes').then(m => m.ROUTES) },
            { path: 'catalogs', loadChildren: () => import('./catalogs/routes').then(m => m.ROUTES) },
            { path: 'variants', loadChildren: () => import('./variants/routes').then(m => m.ROUTES) },
            { path: 'accounts', loadChildren: () => import('./accounts/routes').then(m => m.ROUTES) },
            { path: 'costs', loadChildren: () => import('./costs/routes').then(m => m.ROUTES) },
            { path: 'cheque-portfolio', loadChildren: () => import('./cheque-portfolio/routes').then(m => m.ROUTES) },
            { path: 'bond-portfolio', loadChildren: () => import('./bond-portfolio/routes').then(m => m.ROUTES) },
            { path: 'credits', loadChildren: () => import('./credits/routes').then(m => m.ROUTES) },
            { path: 'employees', loadChildren: () => import('./employees/routes').then(m => m.ROUTES) },
            { path: 'projects', loadChildren: () => import('./projects/routes').then(m => m.ROUTES) },
            { path: 'assets', loadChildren: () => import('./assets/routes').then(m => m.ROUTES) },
            { path: 'bulk-sales', loadChildren: () => import('./bulk-sales/routes').then(m => m.ROUTES) },
            { path: 'e-commerce-settings', loadChildren: () => import('./e-commerce-settings/routes').then(m => m.ROUTES) },
            { path: 'product-match', loadChildren: () => import('./product-match/routes').then(m => m.ROUTES) },
            { path: 'product-list', loadChildren: () => import('./product-list/routes').then(m => m.ROUTES) },
            { path: 'marketplace-prices', loadChildren: () => import('./marketplace-prices/routes').then(m => m.ROUTES) },
            { path: 'marketplace-messages', loadChildren: () => import('./marketplace-messages/routes').then(m => m.ROUTES) },
            { path: 'e-commerce-portal', loadChildren: () => import('./e-commerce-portal/routes').then(m => m.ROUTES) },
            { path: 'cargo-settings', loadChildren: () => import('./cargo-settings/routes').then(m => m.ROUTES) },
            { path: 'e-commerce-settlement', loadChildren: () => import('./e-commerce-settlement/routes').then(m => m.ROUTES) },


            { path: 'bizim-siparis', loadChildren: () => import('./bizim-siparis/routes').then(m => m.ROUTES) },
            { path: 'bizim-muhasebeci', loadChildren: () => import('./bizim-muhasebeci/routes').then(m => m.ROUTES) },
            { path: 'document-reports', loadChildren: () => import('./document-reports/routes').then(m => m.ROUTES) },
            { path: 'financial-reports', loadChildren: () => import('./financial-reports/routes').then(m => m.ROUTES) },
            { path: 'stock-reports', loadChildren: () => import('./stock-reports/routes').then(m => m.ROUTES) },
            { path: 'store-list-report', loadChildren: () => import('./store-list-report/routes').then(m => m.ROUTES) },
            { path: 'advantages', loadChildren: () => import('./advantages/routes').then(m => m.ROUTES) },
            { path: 'users', loadChildren: () => import('./users/routes').then(m => m.ROUTES) },
            { path: 'master-data', loadChildren: () => import('./master-data/routes').then(m => m.ROUTES) },
            { path: 'e-invoice-application', loadChildren: () => import('./e-invoice-application/routes').then(m => m.ROUTES) },
            { path: 'e-invoice-settings', loadChildren: () => import('./e-invoice-settings/routes').then(m => m.ROUTES) },
            { path: 'pos-settings', loadChildren: () => import('./pos-settings/routes').then(m => m.ROUTES) },
            { path: 'proposal-templates', loadChildren: () => import('./proposal-templates/routes').then(m => m.ROUTES) },
            { path: 'label-templates', loadChildren: () => import('./label-templates/routes').then(m => m.ROUTES) },
            { path: 'sms-settings', loadChildren: () => import('./sms-settings/routes').then(m => m.ROUTES) },
            { path: 'support', loadChildren: () => import('./support/routes').then(m => m.ROUTES) },



            { path: 'test', loadChildren: () => import('./test/routes').then(m => m.ROUTES) },
        ]
    },
];