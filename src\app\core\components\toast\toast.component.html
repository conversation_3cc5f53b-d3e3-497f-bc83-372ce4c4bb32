<p-toast key="top-right" position="top-right">
    <ng-template #message let-message>
        <ng-container *ngTemplateOutlet="customToast; context: {message: message}"></ng-container>
    </ng-template>
</p-toast>
<p-toast key="top-left" position="top-left">
    <ng-template #message let-message>
        <ng-container *ngTemplateOutlet="customToast; context: {message: message}"></ng-container>
    </ng-template>
</p-toast>
<p-toast key="bottom-right" position="bottom-right">
    <ng-template #message let-message>
        <ng-container *ngTemplateOutlet="customToast; context: {message: message}"></ng-container>
    </ng-template>
</p-toast>
<p-toast key="bottom-left" position="bottom-left">
    <ng-template #message let-message>
        <ng-container *ngTemplateOutlet="customToast; context: {message: message}"></ng-container>
    </ng-template>
</p-toast>

<ng-template #customToast let-message="message">
    @if(message) {
    <div class="w-full flex flex-1 gap-3 text-white text-base font-normal items-center">
        @switch (message.severity) {
        @case ('success') {
        <bh-icon name="checkmark" class=" w-5 h-5" [size]="20"></bh-icon>
        }
        @case ('error') {
        <bh-icon name="alert-circle" class="w-5 h-5" [size]="20"></bh-icon>
        }
        @case ('warn') {
        <bh-icon name="alert-circle" class="w-5 h-5" [size]="20"></bh-icon>
        }
        @case ('info') {
        <bh-icon name="alert-circle" class="w-5 h-5" [size]="20"></bh-icon>
        }
        }
        <span class="flex-1">{{ message.summary }}</span>
        @if(message.action) {
        <p-button variant="text" size="small" class="action-button" label="{{message.action}}"
            (click)="onToastAction()">
        </p-button>
        }
    </div>
    }
</ng-template>