/* You can add global styles to this file, and also import other style files */
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Poppins:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap');

:root {
  --font-inter: 'Inter', sans-serif;
  --font-poppins: 'Poppins', sans-serif;

  // --sidebar-width: 312px;

  --h1-font-size: 2rem;
  --h1-line-height: 2.75rem;
  --h2-font-size: 1.75rem;
  --h2-line-height: 2.5rem;
  --h3-font-size: 1.5rem;
  --h3-line-height: 2.125rem;
  --h4-font-size: 1.25rem;
  --h4-line-height: 1.625rem;
  --h5-font-size: 1.25rem;
  --h5-line-height: 1.625rem;

  --scrollbar-width: 8px;
  --scrollbar-track-color: #B7FF8B;
  --scrollbar-thumb-color: #366660;

  --sidebar-width: 312px;
  --sidebar-collapsed-width: 110px;
  --toolbar-height: 64px;
}

body {
  font-family: var(--font-inter);
  color: var(--color-coolgray-900);
}

*::-webkit-scrollbar {
  height: var(--scrollbar-width);
  width: var(--scrollbar-width);
}

*::-webkit-scrollbar-track {
  background-color: var(--scrollbar-track-color);
}

*::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-color);
}

h1 {
  font-size: var(--h1-font-size);
  line-height: var(--h1-line-height);
  font-weight: 600;
}

h2 {
  font-size: var(--h2-font-size);
  line-height: var(--h2-line-height);
  font-weight: 600;
}

h3 {
  font-size: var(--h3-font-size);
  line-height: var(--h3-line-height);
  font-weight: 600;
}

h4 {
  font-size: var(--h4-font-size);
  line-height: var(--h4-line-height);
  font-weight: 500;
}

h5 {
  font-size: var(--h5-font-size);
  line-height: var(--h5-line-height);
  font-weight: 400;
}

.cdk-drag-preview {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  opacity: 0.8;
}

.cdk-drag-placeholder {
  opacity: 0.3;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

body {
  bh-splash-screen {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--color-coolgray-1000);
    color: var(--color-white);
    z-index: 999999;
    pointer-events: none;
    opacity: 1;
    visibility: visible;
    transition: opacity 400ms cubic-bezier(0.4, 0, 0.2, 1);

    img {
      width: calc(var(--spacing) * 30);
      max-width: calc(var(--spacing) * 30);
    }

    .spinner {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: calc(var(--spacing) * 10);
      width: calc(var(--spacing) * 14);

      >div {
        width: calc(var(--spacing) * 3);
        height: calc(var(--spacing) * 3);
        background-color: var(--color-blue-400);
        border-radius: 100%;
        display: inline-block;
        -webkit-animation: bh-bouncedelay 1s infinite ease-in-out both;
        animation: bh-bouncedelay 1s infinite ease-in-out both;
      }

      .bounce1 {
        -webkit-animation-delay: -0.32s;
        animation-delay: -0.32s;
      }

      .bounce2 {
        -webkit-animation-delay: -0.16s;
        animation-delay: -0.16s;
      }
    }
  }

  &.bh-splash-screen-hidden {
    bh-splash-screen {
      visibility: hidden;
      opacity: 0;
    }
  }

  &:not(.bh-splash-screen-hidden) {
    overflow: hidden;
  }
}

@-webkit-keyframes bh-bouncedelay {

  0%,
  80%,
  100% {
    -webkit-transform: scale(0)
  }

  40% {
    -webkit-transform: scale(1.0)
  }
}

@keyframes bh-bouncedelay {

  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1.0);
    transform: scale(1.0);
  }
}