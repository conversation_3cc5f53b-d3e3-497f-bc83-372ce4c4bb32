/* You can add global styles to this file, and also import other style files */
@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Poppins:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap');

:root {
  --font-inter: 'Inter', sans-serif;
  --font-poppins: 'Poppins', sans-serif;

  // --sidebar-width: 312px;

  --h1-font-size: 2rem;
  --h1-line-height: 2.75rem;
  --h2-font-size: 1.75rem;
  --h2-line-height: 2.5rem;
  --h3-font-size: 1.5rem;
  --h3-line-height: 2.125rem;
  --h4-font-size: 1.25rem;
  --h4-line-height: 1.625rem;
  --h5-font-size: 1.25rem;
  --h5-line-height: 1.625rem;

  --scrollbar-width: 8px;
  --scrollbar-track-color: #B7FF8B;
  --scrollbar-thumb-color: #366660;

  --sidebar-width: 312px;
  --sidebar-collapsed-width: 110px;
  --toolbar-height: 64px;
}

body {
  font-family: var(--font-inter);
  color: var(--color-coolgray-900);
}

*::-webkit-scrollbar {
  height: var(--scrollbar-width);
  width: var(--scrollbar-width);
}

*::-webkit-scrollbar-track {
  background-color: var(--scrollbar-track-color);
}

*::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb-color);
}

h1 {
  font-size: var(--h1-font-size);
  line-height: var(--h1-line-height);
  font-weight: 600;
}

h2 {
  font-size: var(--h2-font-size);
  line-height: var(--h2-line-height);
  font-weight: 600;
}

h3 {
  font-size: var(--h3-font-size);
  line-height: var(--h3-line-height);
  font-weight: 600;
}

h4 {
  font-size: var(--h4-font-size);
  line-height: var(--h4-line-height);
  font-weight: 500;
}

h5 {
  font-size: var(--h5-font-size);
  line-height: var(--h5-line-height);
  font-weight: 400;
}

.cdk-drag-preview {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  opacity: 0.8;
}

.cdk-drag-placeholder {
  opacity: 0.3;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

/* PrimeNG Components */
@layer components {

  /* Button */
  .p-button {
    border-radius: var(--radius-sm);
    font-size: var(--text-base);
    gap: calc(var(--spacing) * 3);
    font-weight: var(--font-weight-medium);
    line-height: var(--text-base--line-height);
    padding: calc(var(--spacing) * 2) calc(var(--spacing) * 4);

    .p-button-label {
      font-weight: var(--font-weight-medium);
      line-height: var(--text-base--line-height);
    }

    &:disabled {
      background-color: var(--color-coolgray-200);
      color: var(--color-coolgray-400);
      border-color: transparent;

      &:hover {
        background-color: var(--color-coolgray-200);
        color: var(--color-coolgray-400);
        border-color: transparent;
      }
    }
  }

  .p-button-primary {
    background-color: var(--color-coolgray-1000);
    color: var(--color-white);
    border-color: transparent;

    &:hover {
      background-color: var(--color-coolgray-900);
    }

    &:focus-visible {
      background-color: var(--color-coolgray-1000);
      outline-color: var(--color-coolgray-800);
    }
  }

  .p-button-secondary {
    background-color: var(--color-blue-500);
    color: var(--color-white);
    border-color: transparent;

    &:hover {
      background-color: var(--color-blue-700);
    }

    &:focus-visible {
      background-color: var(--color-blue-500);
      outline-color: var(--color-blue-500);
    }
  }

  .p-button-outlined {
    color: var(--color-coolgray-800);
    border-color: var(--color-coolgray-800);
    background-color: transparent;

    &:hover {
      border-color: var(--color-coolgray-1000);
      color: var(--color-coolgray-1000);
    }

    &:focus-visible {
      border-color: var(--color-coolgray-800);
      outline-color: var(--color-coolgray-800);
      color: var(--color-coolgray-900);
    }
  }

  .p-button-text {
    color: var(--color-coolgray-800);
    border-color: transparent;
    background-color: transparent;

    &:hover {
      color: var(--color-coolgray-1000);
    }

    &:focus-visible {
      outline-color: var(--color-coolgray-800);
      color: var(--color-coolgray-900);
    }

    &:disabled {
      background-color: transparent;
      color: var(--color-coolgray-400);
      border-color: transparent;

      &:hover {
        background-color: transparent;
      }
    }
  }

  .p-button-lg {
    padding: calc(var(--spacing) * 3) calc(var(--spacing) * 6);
  }

  .p-button-sm {
    padding: calc(var(--spacing) * 1) calc(var(--spacing) * 4);
  }

  .p-button-xs {
    padding: calc(var(--spacing) * 1) calc(var(--spacing) * 1);
    font-size: var(--text-sm);
  }

  /* Menu */
  .p-menu-overlay {
    box-shadow: 0px var(--spacing) calc(var(--spacing) * 5) calc(var(--spacing) / 4) var(--color-coolgray-200);
    margin-top: var(--spacing);
    border-radius: var(--radius-sm);
    border: calc(var(--spacing) / 4) solid var(--color-coolgray-300);
  }
}