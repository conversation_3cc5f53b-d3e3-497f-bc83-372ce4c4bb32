import { Component, inject, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuItemComponent } from '../menu-item/menu-item.component';
import { SidebarService } from '../sidebar/sidebar.service';

@Component({
    selector: 'bh-menu',
    templateUrl: './menu.component.html',
    styles: [`
        :host {
            overflow-y: auto;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none;  /* Internet Explorer 10+ */
        }   
        :host::-webkit-scrollbar {
            display: none; /* Safari and Chrome */ 
        }
    `],
    imports: [MenuItemComponent],
})
export class MenuComponent implements OnInit {
    sidebarService: SidebarService = inject(SidebarService);
    router: Router = inject(Router);
    
    constructor() { }

    ngOnInit(): void { 
        console.log('MenuComponent initialized with items:', new Date().getMilliseconds(), this.router.url);
    }
}