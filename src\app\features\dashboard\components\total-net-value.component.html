<div class="flex flex-col gap-4 bg-white min-h-34 min-w-160">
    <span class="text-base font-medium">Toplam Net Değer</span>

    @if(loading()) {
    <div>
        <div class="h-4 bg-linear-to-r from-coolgray-200 to-coolgray-50 rounded-sm w-32 ml-auto mr-auto"></div>
    </div>
    <div class="flex flex-row gap-4 border-b border-b-coolgray-100 pb-4">
        <div class="flex-1 border-r border-r-coolgray-100 h-33 pr-3">
            <div class="h-full bg-linear-to-r from-coolgray-200 to-coolgray-50 rounded-sm w-full"></div>
        </div>

        <div class="flex-1 h-33">
            <div class="h-full bg-linear-to-r from-coolgray-200 to-coolgray-50 rounded-sm w-full"></div>
        </div>
    </div>

    <div class="flex flex-row gap-4">
        <div class="flex-1 h-12 border-r border-r-coolgray-100 pr-3">
            <div class="h-full bg-linear-to-r from-coolgray-200 to-coolgray-50 rounded-sm w-full"></div>
        </div>

        <div class="flex-1 h-12">
            <div class="h-full bg-linear-to-r from-coolgray-200 to-coolgray-50 rounded-sm w-full"></div>
        </div>
    </div>

    }
    @else {

    <div class="text-center text-coolgray-400 text-xs">
        @if(showValues()) {
        {{ total | number:'1.2-2'}} {{ currency() }}
        }
        @else {
        0
        }
    </div>

    <div class="flex flex-row gap-4 border-b border-b-coolgray-100 pb-4">
        <div class="flex flex-1 flex-col gap-2 border-r border-r-coolgray-100">

            @for (debt of debtSeries(); track $index) {
            <div class="flex gap-1 items-center justify-between pr-4">
                <a [routerLink]="debt.link"
                    class="flex-1 text-coolgray-700 text-sm hover:underline hover:font-medium cursor-pointer">{{debt.label}}</a>
                <div class="flex-1">
                    <div pTooltip="{{debt.label}} {{showValues() ? (debt.value | number:'1.2-2') : '*******'}} {{ currency() }}"
                        tooltipPosition="top" [style.width]="debt.percentage == 0 ? '6px' : debt.percentage + '%'"
                        class="ml-auto h-1.5 rounded-xs min-w-1.5" [ngClass]="{
                            'bg-coolgray-200': debt.percentage == 0,
                            'bg-red-500': debt.percentage > 0 || (debt.percentage == 0 && debt.value > 0)
                        }">
                    </div>
                </div>

            </div>
            }

        </div>
        <div class="flex flex-1 flex-col gap-2">
            @for (stock of stockSeries(); track $index) {
            <div class="flex gap-1 items-center justify-between pr-4">
                <div class="flex-1">
                    <div pTooltip="{{stock.label}} {{showValues() ? (stock.value | number:'1.2-2') : '*******'}} {{ currency() }}"
                        tooltipPosition="top" [style.width]="stock.percentage == 0 ? '6px' : stock.percentage + '%'"
                        class="h-1.5 rounded-xs min-w-1.5" [ngClass]="{
                            'bg-coolgray-200': stock.percentage == 0,
                            'bg-forest-500': stock.percentage > 0 || (stock.percentage == 0 && stock.value > 0)
                        }">
                    </div>
                </div>
                <a [routerLink]="stock.link"
                    class="flex-1 text-coolgray-700 text-sm text-right hover:underline hover:font-medium cursor-pointer">{{stock.label}}</a>
            </div>
            }

        </div>
    </div>

    <div class="flex flex-row gap-4">

        <div class="flex flex-1 flex-col gap-2 border-r border-r-coolgray-100">
            <div class="flex gap-1 items-center">
                <div class="w-2 h-2 rounded-xs bg-red-500"></div>
                <span class="text-coolgray-800 text-sm">Borç</span>
            </div>
            <div class="flex flex-row gap-2 items-center">
                <h4>-
                    @if (showValues()) {
                    {{ debt() | number:'1.2-2'}}
                    }
                    @else {
                    *******
                    }
                </h4>{{ currency() }}
                @if(debtChange(); as debtChange) {
                <span class="text-coolgray-800 text-xs">
                    geçen aya kıyasla
                </span>

                <div class="py-0.5 px-1.5 rounded-full font-medium text-xs" [ngClass]="{
                    'bg-red-100': debtChange >= 0,
                    'bg-lime-300': debtChange < 0
                }">
                    @if(debtChange < 0) { <span class="text-forest-600">-%{{getAbsValue(debtChange)}}</span>
                        }
                        @else {
                        <span class="text-red-500">+%{{getAbsValue(debtChange)}}</span>
                        }
                </div>
                }
            </div>
        </div>

        <div class="flex flex-1 flex-col gap-2">
            <div class="flex gap-1 items-center">
                <div class="w-2 h-2 rounded-xs bg-forest-500"></div>
                <span class="text-coolgray-800 text-sm">Varlık</span>
            </div>
            <div class="flex flex-row gap-2 items-center">
                <h4>+
                    @if(showValues()) {
                    {{ stock() | number:'1.2-2'}}
                    }
                    @else {
                    *******
                    }
                </h4>{{ currency() }}
                @if(stockChange(); as stockChange){
                <span class="text-coolgray-800 text-xs">
                    geçen aya kıyasla
                </span>

                <div class="py-0.5 px-1.5 rounded-full font-medium text-xs" [ngClass]="{
                    'bg-lime-300': stockChange >= 0,
                    'bg-red-100': stockChange < 0
                }">
                    @if(stockChange >=0) {
                    <span class="text-forest-600">+%{{getAbsValue(stockChange)}}</span>
                    }
                    @else {
                    <span class="text-red-500">-%{{getAbsValue(stockChange)}}</span>
                    }
                </div>
                }
            </div>
        </div>
    </div>
    }
</div>