<div class="flex flex-col gap-4 bg-white min-h-34 min-w-175">
    <span class="text-base font-medium">Toplam Net Değer</span>
    <div class="text-center text-coolgray-400 text-xs">
        {{ total | number:'1.2-2'}} {{ currency() }}
    </div>

    <div class="flex flex-row gap-4 border-b border-b-coolgray-100 pb-4">
        <div class="flex flex-1 flex-col gap-2 border-r border-r-coolgray-100">

            @for (debt of debtSeries(); track $index) {
            <div class="flex gap-1 items-center justify-between pr-4">
                <a [routerLink]="debt.link"
                    class="flex-1 text-coolgray-700 text-sm hover:underline hover:font-medium cursor-pointer">{{debt.label}}</a>
                <div class="flex-1">
                    <div pTooltip="{{debt.label}} {{debt.value | number:'1.2-2'}} {{ currency() }}"
                        tooltipPosition="top" [style.width]="debt.percentage == 0 ? '6px' : debt.percentage + '%'"
                        class="ml-auto h-1.5 rounded-xs min-w-1.5" [ngClass]="{
                            'bg-coolgray-200': debt.percentage == 0,
                            'bg-red-500': debt.percentage > 0
                        }">
                    </div>
                </div>

            </div>
            }

        </div>
        <div class="flex flex-1 flex-col gap-2">
            @for (stock of stockSeries(); track $index) {
            <div class="flex gap-1 items-center justify-between pr-4">
                <div class="flex-1">
                    <div pTooltip="{{stock.label}} {{stock.value | number:'1.2-2'}} {{ currency() }}"
                        tooltipPosition="top" [style.width]="stock.percentage == 0 ? '6px' : stock.percentage + '%'"
                        class="h-1.5 rounded-xs min-w-1.5" [ngClass]="{
                            'bg-coolgray-200': stock.percentage == 0,
                            'bg-forest-500': stock.percentage > 0
                        }">
                    </div>
                </div>
                <a [routerLink]="stock.link"
                    class="flex-1 text-coolgray-700 text-sm text-right hover:underline hover:font-medium cursor-pointer">{{stock.label}}</a>
            </div>
            }

        </div>
    </div>

    <div class="flex flex-row gap-4">

        <div class="flex flex-1 flex-col gap-2 border-r border-r-coolgray-100">
            <div class="flex gap-1 items-center">
                <div class="w-2 h-2 rounded-xs bg-red-500"></div>
                <span class="text-coolgray-800 text-sm">Borç</span>
            </div>
            <div class="flex flex-row gap-2 items-center">
                <h4>- {{ debt() | number:'1.2-2'}} </h4>{{ currency() }}
                <span class="text-coolgray-800 text-xs">
                    geçen aya kıyasla
                </span>
                <div class="p-0.5 rounded-full font-medium text-xs" [ngClass]="{
                    'bg-red-100': debtChangeType() === 'increase',
                    'bg-lime-300': debtChangeType() === 'decrease'
                }">
                    @if(debtChangeType() === 'decrease') {
                    <span class="text-forest-600">-{{debtChange()}}%</span>
                    }
                    @else {
                    <span class="text-red-500">+{{debtChange()}}%</span>
                    }
                </div>
            </div>
        </div>

        <div class="flex flex-1 flex-col gap-2">
            <div class="flex gap-1 items-center">
                <div class="w-2 h-2 rounded-xs bg-forest-500"></div>
                <span class="text-coolgray-800 text-sm">Varlık</span>
            </div>
            <div class="flex flex-row gap-2 items-center">
                <h4>+ {{ stock() | number:'1.2-2'}} </h4>{{ currency() }}
                <span class="text-coolgray-800 text-xs">
                    geçen aya kıyasla
                </span>
                <div class="p-0.5 rounded-full font-medium text-xs" [ngClass]="{
                    'bg-lime-300': stockChangeType() === 'increase',
                    'bg-red-100': stockChangeType() === 'decrease'
                }">
                    @if(stockChangeType() === 'increase') {
                    <span class="text-forest-600">+{{stockChange()}}%</span>
                    }
                    @else {
                    <span class="text-red-500">-{{stockChange()}}%</span>
                    }
                </div>
            </div>
        </div>
    </div>
</div>