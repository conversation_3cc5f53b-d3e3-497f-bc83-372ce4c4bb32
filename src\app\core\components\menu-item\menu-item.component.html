<a [routerLink]="item().link" routerLinkActive="active-link"
    (click)="item().children ? sidebarService.toggleMenuItemExpand(item()) : sidebarService.menuItemClicked(item(), isChild())"
    class="overflow-hidden hover:bg-white-50 relative flex duration-400 cursor-pointer"
    [ngClass]="{'h-[36px]': isChild(), 'h-[46px]': !isChild(), 'font-medium': !isChild(), 'active-link': item().expanded}">

    <div class="flex items-center p-3 absolute left-0 w-[248px]" [ngClass]="{'py-2': isChild()}">
        <div class="w-[20px] duration-400">
            @if (item().icon) {
            <bh-icon [name]="item().icon" [size]="20"></bh-icon>
            }
        </div>

        <span class="flex-1 duration-400 ml-3"
            [ngClass]="{'text-base': !isChild(), 'text-sm': isChild(), 'opacity-0': sidebarService.collapsed, 'opacity-100': !sidebarService.collapsed}">{{
            item().label }}</span>

        @if (item().children) {
        <bh-icon name="chevron-down" [size]="16" class="duration-400 ml-3"
            [ngClass]="{'-rotate-180': item().expanded, 'rotate-0': !item().expanded, 'opacity-0': sidebarService.collapsed, 'opacity-100': !sidebarService.collapsed}"></bh-icon>
        }
    </div>
</a>

<div *ngIf="item().children && !sidebarService.collapsed" class="grid duration-400 ease-in-out"
    [ngClass]="{'mt-2': item().expanded, 'grid-rows-[1fr]': item().expanded, 'grid-rows-[0fr]': !item().expanded}">
    <div class="overflow-hidden flex flex-col gap-y-2">
        @for (child of item().children; track child) {
        <bh-menu-item [item]="child" [isChild]="true"></bh-menu-item>
        }
    </div>
</div>