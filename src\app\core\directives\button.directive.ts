import { Directive, HostBinding, Input } from '@angular/core';

export type ButtonVariant = 'primary' | 'secondary' | 'plain' | 'outlined';

const variantClasses: { [key in ButtonVariant]: string } = {
    primary: 'border-transparent text-white bg-coolgray-1000 hover:bg-coolgray-900 focus:outline-coolgray-800',
    secondary: 'border-transparent bg text-white bg-blue-500 hover:bg-blue-700 focus:outline-blue-500',
    plain: 'border-transparent text-coolgray-900 bg-transparent hover:text-coolgray-1000 focus:outline-coolgray-800 disabled:bg-transparent',
    outlined: 'border-coolgray-800 text-coolgray-900 bg-transparent hover:text-coolgray-1000 hover:border-coolgray-1000 focus:outline-coolgray-800 disabled:border-coolgray-300'
}

export type ButtonSize = 'small' | 'default' | 'large' | 'xsmall';

const sizeClasses: { [key in ButtonSize]: string } = {
    small: 'px-4 py-1',
    default: 'px-4 py-2',
    large: 'px-6 py-3',
    xsmall: 'p-1 text-sm'
}

const base = `inline-flex gap-3 items-center rounded-sm border font-medium cursor-pointer text-base
             focus:outline-1 focus:outline-offset-2 transition-all
            disabled:bg-coolgray-200 disabled:cursor-not-allowed disabled:text-coolgray-400 disabled:focus:outline-0 disabled:active:outline-0`;

const buildTailwindClasses = (variant: ButtonVariant, size: ButtonSize): string =>
    `${base} ${variantClasses[variant]} ${sizeClasses[size]}`;

@Directive({
    selector: '[bhButton]',
    standalone: true
})
export class ButtonDirective {

    private _size: ButtonSize = 'default';
    private _variant: ButtonVariant = 'primary';

    @Input() set size(value: ButtonSize) {
        this._size = value;
        this.class = buildTailwindClasses(this._variant, this._size);
    }

    @Input() set variant(value: ButtonVariant) {
        this._variant = value;
        this.class = buildTailwindClasses(this._variant, this._size);
    }

    @HostBinding('class')
    class = buildTailwindClasses(this._variant, this._size);

    constructor() { }
}