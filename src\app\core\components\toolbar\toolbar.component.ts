import { Component, OnInit } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { ButtonComponent, IconButtonComponent } from '../button/button.component';
import { IconComponent } from "../icon.component";
import { AvatarModule } from 'primeng/avatar';
import { Menu } from "primeng/menu";
import { MenuItem } from 'primeng/api';
import { RippleModule } from 'primeng/ripple';

@Component({
    selector: 'bh-toolbar',
    templateUrl: './toolbar.component.html',
    imports: [ButtonModule, RippleModule, ButtonComponent, IconComponent, IconButtonComponent, AvatarModule, Menu],
})
export class ToolbarComponent implements OnInit {

    title: string = 'Parabasan';
    userMenuItems: MenuItem[] | undefined;
    firmMenuItems: MenuItem[] | undefined;

    constructor() { }

    ngOnInit(): void {
        // Servisten almamız gerekiyor, şu anlık sabit verilerle devam ediyoruz.
        this.userMenuItems = [
            {
                label: 'Hesabım',
                command: () => { }
            },
            {
                label: 'İş Ortaklığı',
                command: () => { }
            },
            {
                label: 'Şifre Değiştir',
                command: () => { }
            },
            {
                label: 'Mobil Giriş',
                command: () => { }
            },
            {
                label: 'Çıkış Yap',
                command: () => { }
            }
        ];

        // Servisten almamız gerekiyor, şu anlık sabit verilerle devam ediyoruz.
        this.firmMenuItems = [
            {
                label: 'Parabasan 2',
                command: () => { }
            },
            {
                label: 'Parabasan 3',
                command: () => { }
            }
        ];
    }
}
