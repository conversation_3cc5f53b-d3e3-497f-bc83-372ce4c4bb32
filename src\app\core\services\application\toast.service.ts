import { EventEmitter, Injectable } from '@angular/core';
import { MessageService, ToastMessageOptions } from 'primeng/api';

@Injectable({ providedIn: 'root' })
export class ToastService {

    onToastActionClicked = new EventEmitter<void>();

    constructor(private messageService: MessageService) { }

    /**
     * Show a toast with optional actionType and payload.
     * @param message PrimeNG MessageOptions object, plus optional action
     */
    private show(message: ToastMessageOptions & { action?: string }) {
        message.sticky = message.action ? true : false;
        if (!message.action)
            message.life = 5000;
        this.messageService.add(message);
    }

    /**
     * Show a success toast
     * @param summary The toast summary
     * @param action The action button label (optional)
     * @param position The toast position (optional, default: 'top-right')
     */
    success(summary: string, action?: string, position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' = 'top-right') {
        this.show({ severity: 'success', summary, action, key: position });
    }

    /**
     * Show an error toast
     * @param summary The toast summary
     * @param action The action button label (optional)
     * @param position The toast position (optional, default: 'top-right')
     */
    error(summary: string, action?: string, position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' = 'top-right') {
        this.show({ severity: 'error', summary, action, key: position });
    }

    /**
     * Show a warning toast
     * @param summary The toast summary
     * @param action The action button label (optional)
     * @param position The toast position (optional, default: 'top-right')
     */
    warning(summary: string, action?: string, position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' = 'top-right') {
        this.show({ severity: 'warn', summary, action, key: position });
    }

    /**
     * Show an info toast
     * @param summary The toast summary
     * @param action The action button label (optional)
     * @param position The toast position (optional, default: 'top-right')
     */
    info(summary: string, action?: string, position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' = 'top-right') {
        this.show({ severity: 'info', summary, action, key: position });
    }

    /**
     * Emit a toast action
     */
    emitAction() {
        this.messageService.clear();
        this.onToastActionClicked.emit();
    }
}