import { Component, inject, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

import { IconComponent } from "../icon.component";

import { MenuItem, SidebarService } from '../sidebar/sidebar.service';

@Component({
  selector: 'bh-menu-item',
  templateUrl: './menu-item.component.html',
  styles: [`
        :host {
          position: relative;
        }

        .active-link {
            background: var(--color-white-50);
        }

        .active-link>div>*:first-child {
            color: var(--color-lime-600);
        }

        :host:has(bh-menu-item>a.active-link)>a {
            background: var(--color-white-50);
        }
        
        :host:has(bh-menu-item>a.active-link)>a>div>*:first-child {
            color: var(--color-lime-600);
        }
    `],
  imports: [CommonModule, RouterModule, IconComponent],
})
export class MenuItemComponent {
  sidebarService: SidebarService = inject(SidebarService);

  item = input.required<MenuItem>();
  isChild = input<boolean>(false);
}