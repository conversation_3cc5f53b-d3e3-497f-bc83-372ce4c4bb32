
import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { ExchangeRateModel } from '../models/exchange-rate.model';
import { DailyIncomeAndSalesModel } from '../models/daily-income-and-sales.model';
import { TotalNetValueModel } from '../models/total-net-value-item.model';
import { PaymentItemModel } from '../models/payment-item.model';
import { CostItemModel, CostResultModel } from '../models/cost-item.model';
import { GiroModel } from '../models/giro.model';
import { environment } from '../../../../environments/environment';
import { ApiHttpService } from '../../../core/services/api-http.service';

@Injectable({
  providedIn: 'root'
})
export class DashboardDataService {

  private _httpClient: HttpClient = inject(HttpClient);
  private httpService: ApiHttpService = inject(ApiHttpService);

  constructor() { }

  getExchangeRates() {
    return this.httpService.get<ExchangeRateModel[]>('/dashboard/exchangeRates');
  }

  getDailyIncomeAndSales() {
    return this.httpService.get<DailyIncomeAndSalesModel>('/dashboard/dailyIncomeAndSales');
  }

  getTotalNetValue() {
    return this.httpService.get<TotalNetValueModel>('/dashboard/totalNetValues');
  }

  getUpcomingPayments() {
    return this.httpService.get<PaymentItemModel[]>('/dashboard/upcomingPayments');
  }

  getOverduePayments() {
    return this.httpService.get<PaymentItemModel[]>('/dashboard/overduePayments');
  }

  getInvoices() {
    return this.httpService.get<CostResultModel>('/dashboard/invoices');
  }

  getCosts() {
    return this.httpService.get<CostResultModel>('/dashboard/costs');
  }

  getCheques() {
    return this.httpService.get<CostResultModel>('/dashboard/cheques');
  }

  getBonds() {
    return this.httpService.get<CostResultModel>('/dashboard/bonds');
  }

  getCredits() {
    return this.httpService.get<CostResultModel>('/dashboard/credits');
  }

  getGiro() {
    return this.httpService.get<GiroModel>('/dashboard/giro');
  }

}