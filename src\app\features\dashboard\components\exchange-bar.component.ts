
import { Component, inject, model, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ExchangeRateModel } from '../models/exchange-rate.model';
import { IconComponent } from '../../../core/components/icon.component';
import { ButtonModule } from 'primeng/button';
import { BaseDashboardComponent } from './base-dashboard.component';
import { DashboardDataService } from '../services/dashboard-data.service';
import { ExchangeRatesDialogComponent } from './exchange-rates-dialog.component';
import { FormDialogService } from '../../../core/services/application/form-dialog.service';

@Component({
    selector: 'bh-exchange-bar',
    template: `
            @if(loading()) {
                <div class="h-5.5 bg-linear-to-r from-coolgray-200 to-coolgray-50 rounded-sm w-96"></div>
            }
            @else{
                @if(exchangeRates()) {
                <div class="flex flex-row gap-3 text-base cursor-pointer items-center @max-md:cursor-default" (click)="showExchangeRatesDialog()">
                    @for (rate of rates; track rate) {
                    <div class="flex gap-2 justify-between items-center">
                        <bh-icon [name]="rate.icon" [size]="16"></bh-icon>
                        <div class="flex gap-1 items-center">
                            <span class="text-coolgray-700 font-normal">A:</span>
                            <span class="font-medium">{{rate.buyingRate | number:'1.3-3'}}</span>
                            
                        </div>
                        <div class="flex gap-1 items-center">
                            <span class="text-coolgray-700 font-normal">S:</span>
                            <span class="font-medium">{{rate.sellingRate | number:'1.3-3'}}</span>
                        </div>
                    </div>
                    }                
                    <bh-icon name="chevron-right" class="@max-md:hidden" [size]="16"></bh-icon>
                </div>
                }
            }
    `,
    imports: [CommonModule, IconComponent, ButtonModule],
    standalone: true,
    providers: [DashboardDataService],
})
export class ExchangeBarComponent extends BaseDashboardComponent implements OnInit {

    dashboardDataService: DashboardDataService = inject(DashboardDataService);
    dialogService = inject(FormDialogService);

    exchangeRates = model<ExchangeRateModel[]>([]);

    get rates() {
        return this.exchangeRates().filter(x => x.currency === 'EUR' || x.currency === 'USD');
    }

    override loadData(): void {
        if (this.exchangeRates().length > 0) return;

        this.loading.set(true);

        this.dashboardDataService.getExchangeRates().subscribe((rates) => {
            this.exchangeRates.set(rates);
            this.loading.set(false);
        });
    }
    ngOnInit(): void {
        this.loadData();
    }

    showExchangeRatesDialog() {
        this.dialogService.open(ExchangeRatesDialogComponent, {
            data: this.exchangeRates(),
            header: 'Döviz Kurları',
            showHeader: true,
            closable: true
        });
    }

}
