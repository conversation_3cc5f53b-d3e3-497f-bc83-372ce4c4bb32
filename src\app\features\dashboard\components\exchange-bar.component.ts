
import { Component, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ExchangeRateModel } from '../models/exchange-rate.model';
import { IconComponent } from '../../../core/components/icon.component';
import { ButtonModule } from 'primeng/button';

@Component({
    selector: 'bh-exchange-bar',
    template: `
            @if(exchangeRates()) {
            <div class="flex flex-row gap-3 text-base cursor-pointer @max-md:cursor-default">
                @for (rate of exchangeRates(); track rate) {
                <div class="flex gap-2 justify-between items-center">
                    <bh-icon [name]="rate.icon" [size]="16"></bh-icon>
                    <div class="flex gap-1 items-center">
                        <span class="text-coolgray-700 font-normal">A:</span>
                        <span class="font-medium">{{rate.buyingRate | number:'1.3-3'}}</span>
                        
                    </div>
                    <div class="flex gap-1 items-center">
                        <span class="text-coolgray-700 font-normal">S:</span>
                        <span class="font-medium">{{rate.sellingRate | number:'1.3-3'}}</span>
                    </div>
                </div>
                }                
                <bh-icon name="chevron-right" class="@max-md:hidden" [size]="16"></bh-icon>
            </div>
            }
    `,
    imports: [CommonModule, IconComponent, ButtonModule],
    standalone: true,
})
export class ExchangeBarComponent {

    exchangeRates = input<ExchangeRateModel[]>();
}
