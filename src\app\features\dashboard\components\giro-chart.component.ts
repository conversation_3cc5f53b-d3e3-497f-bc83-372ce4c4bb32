
import { Component, inject, model, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChartModule } from 'primeng/chart';
import { ButtonModule } from 'primeng/button';
import { IconComponent } from '../../../core/components/icon.component';
import { RouterModule } from '@angular/router';
import { BaseDashboardComponent } from './base-dashboard.component';
import { DashboardDataService } from '../services/dashboard-data.service';

@Component({
    selector: 'bh-giro-chart',
    templateUrl: './giro-chart.component.html',
    imports: [CommonModule, ChartModule, ButtonModule, IconComponent, RouterModule],
    standalone: true,
})
export class GiroChartComponent extends BaseDashboardComponent implements OnInit {

    dashboardDataService: DashboardDataService = inject(DashboardDataService);

    override loadData(): void {
        if (this.series().length > 0) {
            this.fillChart();
            return;
        }

        this.loading.set(true);

        this.dashboardDataService.getGiro().subscribe((data) => {
            this.series.set(data.series);
            this.currency.set(data.currency);
            this.fillChart();
            this.loading.set(false);
        });
    }

    series = model<number[]>([]);
    currency = model<string>('₺');

    currentMonthName = new Date().toLocaleDateString('tr-TR', { month: 'long', year: 'numeric' });
    get currentMonthValue() {
        if (!this.series().length) return 0;
        return this.series()[this.series().length - 1];
    }

    chartData: any;
    chartOptions: any;

    ngOnInit(): void {
        this.loadData();
    }

    fillChart() {
        const offscreenCanvas = document.createElement('canvas');
        offscreenCanvas.width = 16;
        offscreenCanvas.height = 200;
        const ctx = offscreenCanvas.getContext('2d')!;
        var gradientBG = ctx.createLinearGradient(0, 0, 0, 200);
        gradientBG.addColorStop(0, '#DAE3EE');
        gradientBG.addColorStop(1, 'rgba(255, 255, 255, 0.4)');

        const now = new Date();
        const monthLabels = Array.from({ length: 12 }, (_, i) => {
            const d = new Date(now.getFullYear(), now.getMonth() - 11 + i, 1);
            return d.toLocaleString('tr-TR', { month: 'long', year: 'numeric' });
        });

        const bgColors: (CanvasGradient | string)[] = this.series().map(() => gradientBG);
        bgColors[bgColors.length - 1] = '#366660';

        this.chartData = {
            labels: monthLabels,
            datasets: [
                {
                    label: 'Aylık Toplam Ciro',
                    data: this.series(),
                    backgroundColor: bgColors,
                    tension: 0.4,
                    borderRadius: 2,
                    hoverBackgroundColor: '#366660',
                    maxBarThickness: 16,
                }
            ]
        };

        const currency = this.currency();
        
        this.chartOptions = {
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    borderWidth: 1,
                    displayColors: false,
                    callbacks: {
                        title: function (context: any) {
                            // Return empty string to avoid default title
                            return '';
                        },
                        label: (context: any) => {
                            // Month and giro value in one line
                            const month = context.label;
                            const value = context.parsed.y ?? context.parsed;
                            return `${month}   ${this.showValues() ? value.toLocaleString('tr-TR') : '*******'} ${currency}`;
                        }
                    },
                    // Enable HTML tooltips
                    usePointStyle: false,
                    enabled: true,
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#626F82',
                        font: {
                            size: 10,
                            weight: 400
                        },
                        callback: function (value: number) {
                            return monthLabels[value].split(' ').join('\u2028');
                        }
                    },
                    grid: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: '#626F82',
                        font: {
                            size: 12,
                            weight: 400
                        },
                        callback: function (value: number) {
                            return `${value.toLocaleString('tr-TR')} ${currency}`;
                        }
                    },
                }
            }
        };
    }
}