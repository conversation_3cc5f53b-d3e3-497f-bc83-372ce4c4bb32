import { Component, inject } from '@angular/core';
import { DynamicDialogConfig, DynamicDialogModule, DynamicDialogRef } from 'primeng/dynamicdialog';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon.component';
import { AlertDialogDataModel } from '../../models/alert-dialog-data.model';
import { ButtonComponent, IconButtonComponent } from '../button/button.component';

@Component({
    selector: 'bh-alert-dialog',
    standalone: true,
    imports: [CommonModule, IconComponent, DynamicDialogModule, ButtonComponent, IconButtonComponent],
    templateUrl: './alert-dialog.component.html',
})
export class AlertDialogComponent {

    dialogRef = inject(DynamicDialogRef);
    config = inject(DynamicDialogConfig);
    data: AlertDialogDataModel = this.config.data;

    timerId: any;
    showRevert = false;

    onOk(): void {
        if (this.data.isRevertable) {
            this.showRevert = true;
            this.timerId = setTimeout(() => {
                this.closeDialog(true);
            }, this.data.revertTimeout || 2000);
        }
        else {
            this.closeDialog(true);
        }
    }

    onRevert() {
        this.showRevert = false;
        if (!this.timerId) return;
        clearTimeout(this.timerId);
        this.timerId = null;
    }

    closeDialog(result: boolean) {
        this.dialogRef.close(result);
        this.dialogRef.destroy();
    }

    onCancel(): void {
        if (this.timerId)
            clearTimeout(this.timerId);
        this.closeDialog(false);
    }
}