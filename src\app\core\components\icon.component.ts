import { Component, effect, ElementRef, inject, input, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';

const defaultSize = 20;

@Component({
  selector: 'bh-icon',
  template: ``,
})
export class IconComponent implements OnInit {
  readonly name = input.required<string>();
  readonly size = input<number>(defaultSize);

  http: HttpClient = inject(HttpClient);
  el: ElementRef = inject(ElementRef);

  constructor() {
    effect(() => {
      if (this.name()) {
        this.loadIcon(this.name());
      }
    });
  }

  ngOnInit() {
  }

  loadIcon(name: string) {
    this.http
      .get(`assets/icons/${name}.svg`, { responseType: 'text', headers: { 'skipInterceptor': 'true' } })
      .subscribe((svg: any) => {

        const widthMatch = svg.match(/width="([^"]*)"/);
        const width = widthMatch ? widthMatch[1] : undefined;

        if (width != this.size() || this.size() !== defaultSize) {
          svg = svg.replace(/width="[^"]*"/, `width="${this.size()}"`)
            .replace(/height="[^"]*"/, `height="${this.size()}"`);
        }
        this.el.nativeElement.innerHTML = svg;
      });
  }
}
