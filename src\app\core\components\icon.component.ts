import { Component, ElementRef, inject, Input, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'bh-icon',
  template: ``,
})
export class IconComponent implements OnInit {
  @Input() name: string | undefined = '';
  @Input() size: number = 24;

  http: HttpClient = inject(HttpClient);
  el: ElementRef = inject(ElementRef);

  ngOnInit() {
    if (this.name) {
      this.loadIcon(this.name);
    }
  }

  loadIcon(name: string) {
    this.http
      .get(`assets/icons/${name}.svg`, { responseType: 'text' })
      .subscribe((svg: any) => {
        if (this.size !== 24) {
          this.el.nativeElement.innerHTML = svg.replace(`width="24" height="24" viewBox="0 0 24 24"`, `width="${this.size}" height="${this.size}" viewBox="0 0 ${this.size} ${this.size}"`);
        }
        else {
          this.el.nativeElement.innerHTML = svg;
        }
      });
  }
}
