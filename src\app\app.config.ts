import { ApplicationConfig, provideZoneChangeDetection, LOCALE_ID, importProvidersFrom, ENVIRONMENT_INITIALIZER, inject, provideEnvironmentInitializer } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { providePrimeNG } from 'primeng/config';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { registerLocaleData } from '@angular/common';
import localeTr from '@angular/common/locales/tr';
import { routes } from './app.routes';
import AppPreset from './app.preset';
import { HttpClientInMemoryWebApiModule } from 'angular-in-memory-web-api';
import { FakeBackendService } from './core/services/application/fake-backend.service';
import { environment } from '../environments/environment';
import { DialogService } from 'primeng/dynamicdialog';
import { MessageService } from 'primeng/api';
import { SplashScreenService } from './core/services/application/splash-screen.service';
import { errorInterceptor } from './core/interceptors/error.interceptor';

registerLocaleData(localeTr);

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideHttpClient(withInterceptors([errorInterceptor])),
    provideAnimationsAsync(),
    providePrimeNG({
      theme: {
        preset: AppPreset,
        options: {
          darkModeSelector: false || 'none',
          cssLayer: {
            name: 'primeng',
            order: 'theme, base, primeng'
          }
        }
      },
      ripple: true
    }),
    { provide: LOCALE_ID, useValue: 'tr-TR' },
    importProvidersFrom(HttpClientInMemoryWebApiModule.forRoot(FakeBackendService, {
      delay: 0,
      dataEncapsulation: false,
      passThruUnknownUrl: true,
      apiBase: environment.production ? 'api' : 'api'
    })
    ),
    DialogService,
    MessageService,
    provideEnvironmentInitializer(() => {
      inject(SplashScreenService);
    }),
  ],
};
