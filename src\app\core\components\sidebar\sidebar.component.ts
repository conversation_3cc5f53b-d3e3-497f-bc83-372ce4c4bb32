import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MenuComponent } from '../menu/menu.component';
import { IconComponent } from '../icon.component';
import { SidebarService } from './sidebar.service';

@Component({
    selector: 'bh-sidebar',
    templateUrl: './sidebar.component.html',
    host: {
        '[class.collapsed]': 'sidebarService.collapsed === true',
    },
    styles: [`
        :host {
            min-width: var(--sidebar-width);
            max-width: var(--sidebar-width);
            transition: all 0.4s ease;
        }    
        
        :host.collapsed {
            --sidebar-width: 110px;
        }
        
        :host > aside {
            transition: all 0.4s ease;
        }
    `],
    imports: [CommonModule, MenuComponent, IconComponent]
})
export class SidebarComponent {
    sidebarService: SidebarService = inject(SidebarService);
}
