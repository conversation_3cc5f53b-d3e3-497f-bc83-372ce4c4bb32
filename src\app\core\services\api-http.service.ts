import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({
    providedIn: 'root'
})
export class ApiHttpService {
    private baseUrl = environment.serviceUrl;
    private http = inject(HttpClient);

    private withCredentialsOptions(options?: { headers?: HttpHeaders; params?: HttpParams }) {
        if (!environment.production) return options;
        if (!options) return { withCredentials: true };
        return { ...options, withCredentials: true };
    }

    get<T>(endpoint: string, options?: { headers?: HttpHeaders; params?: HttpParams }): Observable<T> {
        return this.http.get<T>(this.baseUrl + endpoint, this.withCredentialsOptions(options));
    }

    post<T>(endpoint: string, body: any, options?: { headers?: HttpHeaders; params?: HttpParams }): Observable<T> {
        return this.http.post<T>(this.baseUrl + endpoint, body, this.withCredentialsOptions(options));
    }

    put<T>(endpoint: string, body: any, options?: { headers?: HttpHeaders; params?: HttpParams }): Observable<T> {
        return this.http.put<T>(this.baseUrl + endpoint, body, this.withCredentialsOptions(options));
    }

    patch<T>(endpoint: string, body: any, options?: { headers?: HttpHeaders; params?: HttpParams }): Observable<T> {
        return this.http.patch<T>(this.baseUrl + endpoint, body, this.withCredentialsOptions(options));
    }

    delete<T>(endpoint: string, options?: { headers?: HttpHeaders; params?: HttpParams }): Observable<T> {
        return this.http.delete<T>(this.baseUrl + endpoint, this.withCredentialsOptions(options));
    }
}