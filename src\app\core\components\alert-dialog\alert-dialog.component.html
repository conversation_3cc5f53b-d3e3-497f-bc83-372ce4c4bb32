<div class="flex flex-col gap-4" [ngClass]="{
    'p-0': data.showAsConfirm,
    'p-4': !data.showAsConfirm
}">

    <div class="flex justify-between items-center"
        [ngClass]="{'border-b border-coolgray-200 px-4 py-3': data.showAsConfirm}">
        @if(data.showAsConfirm) {
        <span class="text-coolgray-900 font-medium text-lg leading-6">{{data.title}}</span>
        }

        <bh-icon-button name="close" size="xs" variant="plain" (click)="onCancel()"
            [ngClass]="{'absolute top-2 right-2': !data.showAsConfirm}"></bh-icon-button>
    </div>

    @if(!data.showAsConfirm) {
    <!-- Icon based on state -->
    <div class="flex justify-center">
        @switch (data.state) {
        @case ('success') {
        <div class="w-12 h-12 rounded-full bg-success-100 flex items-center justify-center">
            <bh-icon name="checkmark" [size]="50" class="text-success-600"></bh-icon>
        </div>
        }
        @case ('warning') {
        <div class="w-12 h-12 rounded-full bg-warning-100 flex items-center justify-center">
            <bh-icon name="alert" [size]="50" class="text-warning-600"></bh-icon>
        </div>
        }
        @case ('error') {
        <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
            <bh-icon name="close" [size]="50" class="text-red-600"></bh-icon>
        </div>
        }
        @case ('info') {
        <div class="w-12 h-12 rounded-full bg-info-100 flex items-center justify-center">
            <bh-icon name="info" [size]="50" class="text-info-600"></bh-icon>
        </div>
        }
        }
    </div>

    <!-- Title -->
    @if(data.title) {
    <div class="text-center text-coolgray-900 font-medium text-lg">{{data.title}}</div>
    }

    }

    <!-- Message -->
    @if(data.message) {
    <div class="text-coolgray-700" [ngClass]="{'text-center': !data.showAsConfirm, 'px-4': data.showAsConfirm}"
        [innerHTML]="data.message"></div>
    }

    <!-- Buttons -->
    <div class="flex gap-3 justify-center mt-2" [ngClass]="{
            'border-t border-coolgray-200 pt-3 pr-3 mb-3': data.showAsConfirm,
            'mb-4' : !data.showAsConfirm
        }">

        @if (data.isRevertable && showRevert) {
        <bh-button (click)="onRevert()" [ngClass]="{'ml-auto': data.showAsConfirm}" variant="plain">Geri Dön</bh-button>
        <bh-button [loading]="true" variant="primary">Kaydediliyor</bh-button>
        }
        @else{
        @if (data.showCancel) {
        <bh-button [ngClass]="{'ml-auto': data.showAsConfirm}" variant="plain" (click)="onCancel()">{{data.cancelLabel
            || 'İptal'}}</bh-button>
        }
        <bh-button (click)="onOk()">{{data.okLabel || 'Tamam'}}</bh-button>
        }
    </div>
</div>