@import "tailwindcss";
@plugin "tailwindcss-primeui";

@theme {

    /* ############################ */
    /* ###### Neutral Colors ###### */
    /* <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, metin<PERSON> ve gölgelendirmeler gibi dikkat çekici olmayan ama yapıyı oluşturan alanlarda kullanılır. */
    /* ############################ */

    /* White / Transparent */
    --color-white: #FFFFFF;
    --color-white-50: rgba(255, 255, 255, 0.05);
    --color-white-100: rgba(255, 255, 255, 0.1);
    --color-white-200: rgba(255, 255, 255, 0.2);
    --color-white-300: rgba(255, 255, 255, 0.3);
    --color-white-400: rgba(255, 255, 255, 0.4);
    --color-white-500: rgba(255, 255, 255, 0.5);
    --color-white-600: rgba(255, 255, 255, 0.6);
    --color-white-700: rgba(255, 255, 255, 0.7);
    --color-white-800: rgba(255, 255, 255, 0.8);
    --color-white-900: rgba(255, 255, 255, 0.9);

    /* Black / Transparent */
    --color-black: #0D141D;
    --color-black-50: rgba(13, 20, 29, 0.05);
    --color-black-100: rgba(13, 20, 29, 0.1);
    --color-black-200: rgba(13, 20, 29, 0.2);
    --color-black-300: rgba(13, 20, 29, 0.3);
    --color-black-400: rgba(13, 20, 29, 0.4);
    --color-black-500: rgba(13, 20, 29, 0.5);
    --color-black-600: rgba(13, 20, 29, 0.6);
    --color-black-700: rgba(13, 20, 29, 0.7);
    --color-black-800: rgba(13, 20, 29, 0.8);
    --color-black-900: rgba(13, 20, 29, 0.9);

    /* Gray */
    --color-gray-50: #FAFBFB;
    --color-gray-100: #F3F4F4;
    --color-gray-200: #E7E8E9;
    --color-gray-300: #CFD1D4;
    --color-gray-400: #B7BABE;
    --color-gray-500: #93989E;
    --color-gray-600: #70767E;
    --color-gray-700: #585F69;
    --color-gray-800: #404853;
    --color-gray-900: #28313E;
    --color-gray-1000: #101A28;

    /* Cool Gray */
    --color-coolgray-50: #FBFCFD;
    --color-coolgray-100: #F5F7FB;
    --color-coolgray-200: #EDF1F7;
    --color-coolgray-300: #DAE3EE;
    --color-coolgray-400: #C2D0E3;
    --color-coolgray-500: #95A4B9;
    --color-coolgray-600: #768497;
    --color-coolgray-700: #626F82;
    --color-coolgray-800: #485260;
    --color-coolgray-900: #2D3540;
    --color-coolgray-1000: #19212B;

    --color-background: #F0F3F8;


    /* ########################## */
    /* ###### Brand Colors ###### */
    /* ########################## */

    /* Forest Green */
    --color-forest-50: #F4F6F6;
    --color-forest-100: #E9EEED;
    --color-forest-200: #BCCCCA;
    --color-forest-300: #8FAAA6;
    --color-forest-400: #638883;
    --color-forest-500: #366660;
    --color-forest-600: #20554E;
    --color-forest-700: #1A443E;
    --color-forest-800: #153733;
    --color-forest-900: #102B27;
    --color-forest-1000: #0A1C19;

    /* Lime */
    --color-lime-50: #FBFFF9;
    --color-lime-100: #F4FFEE;
    --color-lime-200: #EDFFE2;
    --color-lime-300: #E6FFD6;
    --color-lime-400: #DBFFC5;
    --color-lime-500: #CBFFAC;
    --color-lime-600: #B7FF8B;
    --color-lime-700: #A8EB80;
    --color-lime-800: #9AD675;
    --color-lime-900: #8BC26A;
    --color-lime-1000: #76A55A;

    /* Turqoise */
    --color-turqoise-50: #F6FEFC;
    --color-turqoise-100: #E3FCF7;
    --color-turqoise-200: #D0FAF2;
    --color-turqoise-300: #BDF8ED;
    --color-turqoise-400: #A0F5E5;
    --color-turqoise-500: #7BF1DB;
    --color-turqoise-600: #42EBCC;
    --color-turqoise-700: #3BD3B8;
    --color-turqoise-800: #32B39B;
    --color-turqoise-900: #288D7A;
    --color-turqoise-1000: #227868;

    /* Purple */
    --color-purple-50: #F4F1FF;
    --color-purple-100: #E3DCFF;
    --color-purple-200: #D5CAFF;
    --color-purple-300: #B8A6FF;
    --color-purple-400: #9C83FF;
    --color-purple-500: #8060FF;
    --color-purple-600: #724EFF;
    --color-purple-700: #5B3ECC;
    --color-purple-800: #4A33A6;
    --color-purple-900: #332373;
    --color-purple-1000: #291C5C;


    /* ########################## */
    /* ###### Chart Colors ###### */
    /* ########################## */

    /* Blue */
    --color-blue-50: #EEF5FD;
    --color-blue-100: #DCE9FC;
    --color-blue-200: #BFD9FD;
    --color-blue-300: #8BBBFB;
    --color-blue-400: #65A5F9;
    --color-blue-500: #0668E9;
    --color-blue-600: #0559C7;
    --color-blue-700: #04479E;
    --color-blue-800: #043A82;
    --color-blue-900: #032C63;
    --color-blue-1000: #02224B;

    /* Orange */
    --color-orange-50: #FFF9F1;
    --color-orange-100: #FEF0DD;
    --color-orange-200: #FDE1BB;
    --color-orange-300: #FCCD8F;
    --color-orange-400: #FAB75F;
    --color-orange-500: #F8A12E;
    --color-orange-600: #DE8208;
    --color-orange-700: #C67307;
    --color-orange-800: #A15E06;
    --color-orange-900: #884F05;
    --color-orange-1000: #6D3F04;

    /* Red */
    --color-red-50: #FFF7F5;
    --color-red-100: #FFEBE7;
    --color-red-200: #FEC5B8;
    --color-red-300: #FE9F8B;
    --color-red-400: #FE7E62;
    --color-red-500: #FE5E3B;
    --color-red-600: #E55535;
    --color-red-700: #CB4B2F;
    --color-red-800: #A53D26;
    --color-red-900: #8C3420;
    --color-red-1000: #702A1A;


    /* ############################# */
    /* ###### Semantic Colors ###### */
    /* ############################# */

    /* Success */
    --color-success-50: #F7FCFA;
    --color-success-100: #EAF7F2;
    --color-success-200: #D4F0E4;
    --color-success-300: #AAE1CA;
    --color-success-400: #7FD2AF;
    --color-success-500: #55C395;
    --color-success-600: #2AB47A;
    --color-success-700: #2D976A;
    --color-success-800: #227954;
    --color-success-900: #1A6243;
    --color-success-1000: #0B412A;
    --color-success-1100: #093422;

    /* Warning */
    --color-warning-50: #FFFCF6;
    --color-warning-100: #FFF7E9;
    --color-warning-200: #FFEFD3;
    --color-warning-300: #FFDFA7;
    --color-warning-400: #FFCF7C;
    --color-warning-500: #FFBF50;
    --color-warning-600: #FFAF24;
    --color-warning-700: #DE981F;
    --color-warning-800: #C8881C;
    --color-warning-900: #916213;
    --color-warning-1000: #704B0E;
    --color-warning-1100: #5A3C0B;

    /* Error */
    --color-error-50: #FEF6F7;
    --color-error-100: #FCE9EB;
    --color-error-200: #F9D3D6;
    --color-error-300: #F3A7AE;
    --color-error-400: #ED7A85;
    --color-error-500: #E74E5D;
    --color-error-600: #E12234;
    --color-error-700: #B91C2A;
    --color-error-800: #901521;
    --color-error-900: #680F17;
    --color-error-1000: #540C12;
    --color-error-1100: #430A0E;

    /* Informative */
    --color-info-50: #F2F8FD;
    --color-info-100: #E5F2FC;
    --color-info-200: #BFDEF7;
    --color-info-300: #8CC4F0;
    --color-info-400: #66B1EB;
    --color-info-500: #3397E4;
    --color-info-600: #007DDD;
    --color-info-700: #0064B1;
    --color-info-800: #004B85;
    --color-info-900: #003863;
    --color-info-1000: #002C4D;
    --color-info-1100: #001F37;

    

    --text-base--line-height: 1.375;
    --text-sm--line-height: 20px;
    --text-xs--line-height: 16px;
}