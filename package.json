{"name": "bizimhesapv2-web-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development"}, "private": true, "dependencies": {"@angular/animations": "^19.2.0", "@angular/cdk": "^19.2.0", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@primeng/themes": "^19.1.3", "@tailwindcss/postcss": "^4.1.7", "apexcharts": "^3.41.0", "chart.js": "^4.5.0", "ng-apexcharts": "^1.7.6", "postcss": "^8.5.3", "primeng": "^19.1.3", "rxjs": "~7.8.0", "tailwindcss": "^4.1.7", "tailwindcss-primeui": "^0.6.1", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.0", "@angular/cli": "^19.2.0", "@angular/compiler-cli": "^19.2.0", "typescript": "~5.7.2"}}