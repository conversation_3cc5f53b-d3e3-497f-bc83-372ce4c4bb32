import { Component, ElementRef, Ng<PERSON><PERSON>, inject, input } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { IconComponent } from '../icon.component';
import { RippleModule } from 'primeng/ripple';

type ButtonVariant = 'primary' | 'secondary' | 'plain' | 'outlined';
type ButtonSize = 'default' | 'lg' | 'sm' | 'xs';

const buttonVariantsV2: { [key in ButtonVariant]: string } = {
    primary: '',
    secondary: 'p-button-secondary',
    plain: 'p-button-plain',
    outlined: 'p-button-outlined',
};

const buttonSizesV2: { [key in ButtonSize]: string } = {
    default: '',
    lg: 'p-button-lg',
    sm: 'p-button-sm',
    xs: 'p-button-xs'
};

@Component({
    selector: 'bh-button',
    template: `
        <p-button
            [loading]="loading()"
            [type]="type()"
            [styleClass]="cssClass"
            [disabled]="disabled()"
            label=" "
            >
            <ng-template #content>
                @if(leftIcon() !== '') {
                <bh-icon [name]="leftIcon() || ''" [size]="iconSize" />
                }

                <span>
                    <ng-content></ng-content>
                </span>

                @if(rightIcon() !== '') {
                    <bh-icon [name]="rightIcon() || ''" [size]="iconSize" />
                }
            </ng-template> 
            
            <!-- <ng-template #loadingicon>
                <bh-icon name="loading" [size]="20"></bh-icon>
            </ng-template> -->
        </p-button>
    `,
    styles: [`
        :host {
            display: inline-flex;
        }   
    `],
    imports: [ButtonModule, IconComponent]
})
export class ButtonComponent {
    readonly type = input<'button' | 'submit' | 'reset'>('button');
    readonly variant = input<ButtonVariant>('primary');
    readonly size = input<ButtonSize>('default');
    readonly class = input<string>('');
    readonly rounded = input<boolean>(false);
    readonly loading = input<boolean>(false);

    readonly leftIcon = input<string>('');
    readonly rightIcon = input<string>('');

    readonly disabled = input(false, {
        transform: (value: boolean | string) => typeof value === 'string' ? value === '' : value,
    });

    get cssClass(): string {
        return `${buttonVariantsV2[this.variant()]} ${buttonSizesV2[this.size()]} ${this.rounded() ? 'p-button-rounded' : ''} ${this.class()}`.trim();
    }

    get iconSize(): number {
        if (this.size() === 'xs') return 16;

        return 20;
    }

    el: ElementRef = inject(ElementRef);
    zone: NgZone = inject(NgZone);

    private unlistenFn?: () => void;

    ngOnInit() {
        this.zone.runOutsideAngular(() => {
            const nativeEl = this.el.nativeElement;

            const handler = (event: Event) => {
                if (this.disabled()) {
                    event.preventDefault();
                    event.stopImmediatePropagation();
                }
            };

            nativeEl.addEventListener('click', handler, true);

            this.unlistenFn = () => nativeEl.removeEventListener('click', handler, true);
        });
    }

    ngOnDestroy() {
        if (this.unlistenFn) this.unlistenFn();
    }
}

@Component({
    selector: 'bh-icon-button',
    template: `
        <p-button 
            [type]="type()"
            [styleClass]="cssClass"
            [disabled]="disabled()"
            >
            <ng-template #icon>
                <bh-icon [name]="name()" [size]="iconSize" />    
            </ng-template>
            
        </p-button>
    `,
    styles: [`
        :host {
            display: inline-flex;
        }   
    `],
    imports: [ButtonModule, IconComponent]
})
export class IconButtonComponent {
    readonly type = input<'button' | 'submit' | 'reset'>('button');
    readonly variant = input<ButtonVariant>('primary');
    readonly size = input<ButtonSize>('default');
    readonly name = input.required<string>();
    readonly class = input<string>('');
    readonly rounded = input<boolean>(false);

    readonly disabled = input(false, {
        transform: (value: boolean | string) => typeof value === 'string' ? value === '' : value,
    });

    get cssClass(): string {
        return `p-button p-component ${buttonVariantsV2[this.variant()]} ${buttonSizesV2[this.size()]} p-button-icon-only ${this.rounded() ? 'p-button-rounded' : ''} ${this.class()}`.trim();
    }

    get iconSize(): number {
        if (this.size() === 'xs') return 16;

        return 20;
    }

    el: ElementRef = inject(ElementRef);
    zone: NgZone = inject(NgZone);

    private unlistenFn?: () => void;

    ngOnInit() {
        this.zone.runOutsideAngular(() => {
            const nativeEl = this.el.nativeElement;

            const handler = (event: Event) => {
                if (this.disabled()) {
                    event.preventDefault();
                    event.stopImmediatePropagation();
                }
            };

            nativeEl.addEventListener('click', handler, true);

            this.unlistenFn = () => nativeEl.removeEventListener('click', handler, true);
        });
    }

    ngOnDestroy() {
        if (this.unlistenFn) this.unlistenFn();
    }
}