

import { Component, Input, OnInit, effect, input, output } from '@angular/core';

type ButtonVariant = 'primary' | 'secondary' | 'plain' | 'outlined';

const buttonVariantClasses: { [key in ButtonVariant]: string } = {
    primary: 'border-transparent text-white bg-coolgray-1000 hover:bg-coolgray-900 focus:outline-coolgray-800',
    secondary: 'border-transparent bg text-white bg-blue-500 hover:bg-blue-700 focus:outline-blue-500',
    plain: 'border-transparent text-coolgray-900 bg-transparent hover:text-coolgray-1000 focus:outline-coolgray-800 disabled:bg-transparent',
    outlined: 'border-coolgray-800 text-coolgray-900 bg-transparent hover:text-coolgray-1000 hover:border-coolgray-1000 focus:outline-coolgray-800 disabled:border-coolgray-300'
};

type ButtonSize = 'small' | 'default' | 'large' | 'xsmall';

const buttonSizeClasses: { [key in ButtonSize]: string } = {
    small: 'px-4 py-1',
    default: 'px-4 py-2',
    large: 'px-6 py-3',
    xsmall: 'p-1 text-sm'
};

const buttonCssBase = `inline-flex gap-3 items-center rounded-sm border font-medium cursor-pointer text-base
                        focus:outline-1 focus:outline-offset-2 transition-all
                        disabled:bg-coolgray-200 disabled:cursor-not-allowed disabled:text-coolgray-400 disabled:focus:outline-0 disabled:active:outline-0`;

@Component({
    selector: 'bh-button',
    template: `
                <button [type]="type()" [class]="cssClass" [disabled]="disabled()" (click)="onClick($event)">
                    <ng-content></ng-content>
                </button>
    `,
    standalone: true,
})
export class ButtonComponent implements OnInit {
    readonly type = input<'button' | 'submit' | 'reset'>('button');
    // readonly disabled = input<boolean>(false);
    click = output<any>();

    readonly disabled = input(false, {
        transform: (value: boolean | string) => typeof value === 'string' ? value === '' : value,
    });

    readonly variant = input<ButtonVariant>('primary');
    readonly size = input<ButtonSize>('default');

    readonly class = input<string>('');

    buildTailwindClasses = (variant: ButtonVariant, size: ButtonSize): string =>
        `${buttonCssBase} ${buttonVariantClasses[variant]} ${buttonSizeClasses[size]} ${this.class()}`;

    cssClass = this.buildTailwindClasses(this.variant(), this.size());

    onClick(event: any) {
        event.stopPropagation();
        this.click.emit(event);
    }

    constructor() {
        effect(() => {
            this.cssClass = this.buildTailwindClasses(this.variant(), this.size());
        });

    }

    ngOnInit() { }
}