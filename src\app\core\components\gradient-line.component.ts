import { Component, input } from "@angular/core";

@Component({
    selector: 'bh-gradient-line',
    template: `
    <svg [attr.viewBox]="'0 0 ' + width() + ' ' + height()" [attr.width]="width()" [attr.height]="height()">
        <defs>
            <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="#F5F7FB" />
            <stop offset="100%" stop-color="#768497" />
            </linearGradient>
        </defs>

        <path
            [attr.d]="getSmoothedPath()"
            fill="none"
            stroke="url(#lineGradient)"
            stroke-width="1"
        />
        </svg>
    `,
})
export class GradientLineComponent {

    series = input<number[]>([]);

    width = input<number>(120);
    height = input<number>(30);

    getPoints() {
        const stepX = this.width() / (this.series().length - 1);
        const minY = Math.min(...this.series());
        const maxY = Math.max(...this.series());

        // Flip and scale Y values to fit SVG height
        return this.series().map((y, i) => {
            const x = i * stepX;
            const normalizedY = (y - minY) / (maxY - minY); // normalize 0 to 1
            const svgY = this.height() - normalizedY * this.height(); // flip
            return { x, y: svgY };
        });
    }

    getPathD(): string {
        const points = this.getPoints();
        return points.map((pt, i) =>
            i === 0 ? `M ${pt.x},${pt.y}` : `L ${pt.x},${pt.y}`
        ).join(' ');
    }

    getSmoothedPath(): string {
        const points = this.getPoints();

        if (points.length < 2) return '';

        let d = `M ${points[0].x},${points[0].y}`;

        for (let i = 0; i < points.length - 1; i++) {
            const p0 = points[i];
            const p1 = points[i + 1];
            const midX = (p0.x + p1.x) / 2;
            d += ` C ${midX},${p0.y} ${midX},${p1.y} ${p1.x},${p1.y}`;
        }

        return d;
    }
}