import { Injectable, Type } from '@angular/core';
import { DialogService as PrimeDialogService, DynamicDialogRef } from 'primeng/dynamicdialog';

@Injectable({ providedIn: 'root' })
export class FormDialogService {
    constructor(private primeDialogService: PrimeDialogService) { }

    open<T>(component: Type<T>, config?: {
        data?: any,
        size?: 'small' | 'default' | 'large',
        header?: string,
        styleClass?: string,
        contentStyle?: any,
        modal?: boolean,
        showHeader?: boolean,
        closable?: boolean,
        closeOnEscape?: boolean,
        dismissableMask?: boolean,
        draggable?: boolean,
        resizable?: boolean,
        position?: 'center' | 'top' | 'bottom' | 'left' | 'right',
        breakpoints?: { [key: string]: string },
        style?: any,
        rtl?: boolean,
        autoZIndex?: boolean,
        appendTo?: any,
        transitionOptions?: string,
    }): DynamicDialogRef {
        return this.primeDialogService.open(component, {
            data: config?.data,
            header: config?.header,
            width: config?.size == 'large' ? '800px' : config?.size == 'small' ? '300px' : '500px',
            styleClass: config?.styleClass ?? 'form-dialog',
            contentStyle: config?.contentStyle ?? { 'border-radius': '0px', overflow: 'hidden' },
            modal: config?.modal ?? true,
            baseZIndex: 10000,
            showHeader: config?.showHeader ?? true,
            closable: config?.closable ?? true,
            closeOnEscape: config?.closeOnEscape ?? true,
            dismissableMask: config?.dismissableMask ?? true,
            draggable: config?.draggable ?? false,
            resizable: config?.resizable ?? false,
            position: config?.position ?? 'center',
            breakpoints: config?.breakpoints,
            style: config?.style,
            rtl: config?.rtl ?? false,
            autoZIndex: config?.autoZIndex ?? true,
            appendTo: config?.appendTo,
            transitionOptions: config?.transitionOptions ?? '0.2s ease-in-out'
        });
    }
}